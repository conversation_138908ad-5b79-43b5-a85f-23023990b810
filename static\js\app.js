/**
 * NavStack 主要JavaScript文件
 */

// 全局配置
const NavStack = {
    baseUrl: window.location.origin,
    apiUrl: 'index.php?c=api',
    
    // 初始化
    init() {
        this.initTheme();
        this.initSearch();
        this.initLinkCards();
        this.initTooltips();
        this.bindEvents();
    },
    
    // 初始化主题
    initTheme() {
        const savedTheme = localStorage.getItem('theme') || 'auto';
        this.setTheme(savedTheme);
    },
    
    // 设置主题
    setTheme(theme) {
        const html = document.documentElement;
        
        if (theme === 'auto') {
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            theme = prefersDark ? 'dark' : 'light';
        }
        
        html.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
        
        // 更新Bootstrap主题
        if (theme === 'dark') {
            html.setAttribute('data-bs-theme', 'dark');
        } else {
            html.removeAttribute('data-bs-theme');
        }
    },
    
    // 初始化搜索功能
    initSearch() {
        const searchForm = document.querySelector('form[action*="search"]');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                const input = searchForm.querySelector('input[name="q"]');
                if (!input.value.trim()) {
                    e.preventDefault();
                    this.showMessage('请输入搜索关键词', 'warning');
                }
            });
        }
    },
    
    // 初始化链接卡片
    initLinkCards() {
        const linkCards = document.querySelectorAll('.link-card');
        linkCards.forEach(card => {
            card.addEventListener('click', (e) => {
                if (e.target.tagName !== 'A' && e.target.tagName !== 'BUTTON') {
                    const linkId = card.dataset.linkId;
                    if (linkId) {
                        this.openLink(linkId);
                    }
                }
            });
        });
    },
    
    // 初始化工具提示
    initTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },
    
    // 绑定事件
    bindEvents() {
        // 主题切换
        window.setTheme = (theme) => {
            this.setTheme(theme);
        };
        
        // 记录点击
        window.recordClick = (linkId) => {
            this.recordClick(linkId);
        };
        
        // 监听系统主题变化
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'auto') {
                this.setTheme('auto');
            }
        });
    },
    
    // 打开链接
    openLink(linkId) {
        fetch(`index.php?c=link&action=click&id=${linkId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.open(data.url, '_blank');
                } else {
                    this.showMessage(data.message || '链接无效', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                this.showMessage('网络错误', 'error');
            });
    },
    
    // 记录点击统计
    recordClick(linkId) {
        fetch(`index.php?c=link&action=click&id=${linkId}`, {
            method: 'POST'
        }).catch(error => {
            console.error('Click tracking error:', error);
        });
    },
    
    // 显示消息
    showMessage(message, type = 'info') {
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';
        
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // 查找或创建消息容器
        let messageContainer = document.querySelector('.message-container');
        if (!messageContainer) {
            messageContainer = document.createElement('div');
            messageContainer.className = 'message-container position-fixed top-0 start-50 translate-middle-x';
            messageContainer.style.zIndex = '9999';
            messageContainer.style.marginTop = '20px';
            document.body.appendChild(messageContainer);
        }
        
        messageContainer.innerHTML = alertHtml;
        
        // 自动隐藏
        setTimeout(() => {
            const alert = messageContainer.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    },
    
    // AJAX请求封装
    ajax(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        return fetch(url, finalOptions)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            });
    },
    
    // 格式化数字
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    },
    
    // 复制到剪贴板
    copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showMessage('已复制到剪贴板', 'success');
            }).catch(() => {
                this.fallbackCopyToClipboard(text);
            });
        } else {
            this.fallbackCopyToClipboard(text);
        }
    },
    
    // 备用复制方法
    fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            this.showMessage('已复制到剪贴板', 'success');
        } catch (err) {
            this.showMessage('复制失败', 'error');
        }
        
        document.body.removeChild(textArea);
    },
    
    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    NavStack.init();
});

// 导出到全局
window.NavStack = NavStack;
