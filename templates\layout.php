<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $this->escape($title ?? SITE_NAME) ?></title>
    <meta name="description" content="<?= $this->escape($description ?? SITE_DESCRIPTION) ?>">
    <meta name="keywords" content="导航,网址,书签,工具">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="<?= $this->asset('css/style.css') ?>" rel="stylesheet">
    
    <link rel="icon" type="image/x-icon" href="<?= $this->asset('favicon.ico') ?>">
</head>
<body class="<?= $bodyClass ?? '' ?>">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="<?= $this->url() ?>">
                <i class="fas fa-compass me-2"></i>
                <?= SITE_NAME ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?= $this->isActive('') ? 'active' : '' ?>" href="<?= $this->url() ?>">
                            <i class="fas fa-home"></i> 首页
                        </a>
                    </li>
                </ul>
                
                <!-- 搜索框 -->
                <form class="d-flex me-3" action="<?= $this->url('home', 'search') ?>" method="GET">
                    <div class="input-group">
                        <input class="form-control" type="search" name="q" placeholder="搜索网站..." 
                               value="<?= $this->escape($keyword ?? '') ?>" aria-label="Search">
                        <button class="btn btn-outline-light" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
                
                <!-- 主题切换 -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-palette"></i>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="setTheme('light')">
                            <i class="fas fa-sun"></i> 浅色主题
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="setTheme('dark')">
                            <i class="fas fa-moon"></i> 深色主题
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="setTheme('auto')">
                            <i class="fas fa-adjust"></i> 自动
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <?php if (isset($content)): ?>
            <?= $content ?>
        <?php endif; ?>
    </main>

    <!-- 页脚 -->
    <footer class="footer bg-light mt-5">
        <div class="container py-4">
            <div class="row">
                <div class="col-md-6">
                    <h6><?= SITE_NAME ?></h6>
                    <p class="text-muted small"><?= SITE_DESCRIPTION ?></p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted small">
                        Powered by NavStack &copy; <?= date('Y') ?>
                    </p>
                    <?php if (isset($stats)): ?>
                    <p class="text-muted small">
                        共收录 <?= $stats['total_links'] ?? 0 ?> 个网站，
                        总点击 <?= $stats['total_clicks'] ?? 0 ?> 次
                    </p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?= $this->asset('js/app.js') ?>"></script>
    
    <?php if (isset($scripts)): ?>
        <?= $scripts ?>
    <?php endif; ?>
</body>
</html>
