<?php

namespace Models;

use Core\Model;

/**
 * 链接模型
 */
class Link extends Model
{
    protected $table = 'links';
    
    /**
     * 获取所有启用的链接
     */
    public function getActiveLinks($categoryId = null)
    {
        $where = 'status = 1';
        $params = [];
        
        if ($categoryId !== null) {
            $where .= ' AND category_id = :category_id';
            $params['category_id'] = $categoryId;
        }
        
        return $this->findAll($where, $params, 'sort_order ASC, id ASC');
    }
    
    /**
     * 按分类获取链接
     */
    public function getLinksByCategory()
    {
        $sql = "SELECT l.*, c.name as category_name 
                FROM links l 
                LEFT JOIN categories c ON l.category_id = c.id 
                WHERE l.status = 1 AND c.status = 1 
                ORDER BY c.sort_order ASC, l.sort_order ASC, l.id ASC";
        
        $links = $this->db->fetchAll($sql);
        
        // 按分类分组
        $grouped = [];
        foreach ($links as $link) {
            $categoryId = $link['category_id'];
            if (!isset($grouped[$categoryId])) {
                $grouped[$categoryId] = [
                    'category' => [
                        'id' => $categoryId,
                        'name' => $link['category_name']
                    ],
                    'links' => []
                ];
            }
            $grouped[$categoryId]['links'][] = $link;
        }
        
        return array_values($grouped);
    }
    
    /**
     * 搜索链接
     */
    public function searchLinks($keyword, $limit = 50)
    {
        $where = 'status = 1 AND (title LIKE :keyword OR description LIKE :keyword OR url LIKE :keyword)';
        $params = ['keyword' => "%{$keyword}%"];
        
        return $this->findAll($where, $params, 'click_count DESC, id ASC', $limit);
    }
    
    /**
     * 获取热门链接
     */
    public function getPopularLinks($limit = 10)
    {
        return $this->findAll('status = 1', [], 'click_count DESC, id ASC', $limit);
    }
    
    /**
     * 获取最新链接
     */
    public function getLatestLinks($limit = 10)
    {
        return $this->findAll('status = 1', [], 'created_at DESC', $limit);
    }
    
    /**
     * 增加点击次数
     */
    public function incrementClick($id)
    {
        $sql = "UPDATE {$this->table} SET click_count = click_count + 1 WHERE id = :id";
        return $this->db->query($sql, ['id' => $id]);
    }
    
    /**
     * 检查URL是否存在
     */
    public function urlExists($url, $excludeId = null)
    {
        $where = 'url = :url';
        $params = ['url' => $url];
        
        if ($excludeId) {
            $where .= ' AND id != :id';
            $params['id'] = $excludeId;
        }
        
        return $this->findOne($where, $params) !== false;
    }
    
    /**
     * 获取链接统计信息
     */
    public function getStats()
    {
        $sql = "SELECT 
                    COUNT(*) as total_links,
                    COUNT(CASE WHEN status = 1 THEN 1 END) as active_links,
                    SUM(click_count) as total_clicks,
                    AVG(click_count) as avg_clicks
                FROM {$this->table}";
        
        return $this->db->fetch($sql);
    }
    
    /**
     * 更新排序
     */
    public function updateSort($id, $sortOrder)
    {
        return $this->update($id, ['sort_order' => $sortOrder]);
    }
    
    /**
     * 软删除
     */
    public function softDelete($id)
    {
        return $this->update($id, ['status' => 0]);
    }
    
    /**
     * 恢复链接
     */
    public function restore($id)
    {
        return $this->update($id, ['status' => 1]);
    }
    
    /**
     * 批量更新分类
     */
    public function updateCategory($ids, $categoryId)
    {
        if (empty($ids)) {
            return false;
        }
        
        $placeholders = str_repeat('?,', count($ids) - 1) . '?';
        $sql = "UPDATE {$this->table} SET category_id = ? WHERE id IN ({$placeholders})";
        $params = array_merge([$categoryId], $ids);
        
        return $this->db->query($sql, $params);
    }
}
