<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $this->escape($title ?? 'TwoNav管理后台') ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 管理后台样式 -->
    <link href="<?= $this->asset('css/admin.css') ?>" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: #2c3e50;
            color: white;
        }
        .sidebar .nav-link {
            color: #bdc3c7;
            padding: 0.75rem 1rem;
            border-radius: 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: #34495e;
            color: white;
        }
        .sidebar .nav-link i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            background: #f8f9fa;
            min-height: 100vh;
        }
        .navbar-brand {
            font-weight: 600;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .btn-group-sm > .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">TwoNav管理</h5>
                        <small class="text-muted">v2.0.0</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?= $this->isActive('admin') ? 'active' : '' ?>" 
                               href="<?= $this->url('admin') ?>">
                                <i class="fas fa-tachometer-alt"></i>
                                控制台
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                                <span>站点设置</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $this->isActive('admin', 'settings') ? 'active' : '' ?>" 
                               href="<?= $this->url('admin', 'settings') ?>">
                                <i class="fas fa-cog"></i>
                                基本设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $this->isActive('theme') ? 'active' : '' ?>" 
                               href="<?= $this->url('theme') ?>">
                                <i class="fas fa-palette"></i>
                                主题管理
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                                <span>链接管理</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $this->isActive('link') ? 'active' : '' ?>" 
                               href="<?= $this->url('link') ?>">
                                <i class="fas fa-link"></i>
                                链接列表
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $this->isActive('category') ? 'active' : '' ?>" 
                               href="<?= $this->url('category') ?>">
                                <i class="fas fa-folder"></i>
                                分类管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= $this->url('link', 'add') ?>">
                                <i class="fas fa-plus"></i>
                                添加链接
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                                <span>扩展功能</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $this->isActive('submission') ? 'active' : '' ?>" 
                               href="<?= $this->url('submission') ?>">
                                <i class="fas fa-paper-plane"></i>
                                收录管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $this->isActive('guestbook') ? 'active' : '' ?>" 
                               href="<?= $this->url('guestbook') ?>">
                                <i class="fas fa-comments"></i>
                                留言管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $this->isActive('article') ? 'active' : '' ?>" 
                               href="<?= $this->url('article') ?>">
                                <i class="fas fa-newspaper"></i>
                                文章管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $this->isActive('advertisement') ? 'active' : '' ?>" 
                               href="<?= $this->url('advertisement') ?>">
                                <i class="fas fa-ad"></i>
                                广告管理
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                                <span>网站管理</span>
                            </h6>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $this->isActive('user') ? 'active' : '' ?>" 
                               href="<?= $this->url('user') ?>">
                                <i class="fas fa-users"></i>
                                用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= $this->isActive('admin', 'statistics') ? 'active' : '' ?>" 
                               href="<?= $this->url('admin', 'statistics') ?>">
                                <i class="fas fa-chart-bar"></i>
                                站长工具
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主要内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 顶部导航 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><?= $pageTitle ?? '控制台' ?></h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="<?= $this->url() ?>" class="btn btn-sm btn-outline-secondary" target="_blank">
                                <i class="fas fa-external-link-alt"></i>
                                查看网站
                            </a>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i>
                                <?= $_SESSION['admin_username'] ?? 'admin' ?>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?= $this->url('admin', 'profile') ?>">
                                    <i class="fas fa-user-edit"></i> 个人资料
                                </a></li>
                                <li><a class="dropdown-item" href="<?= $this->url('admin', 'settings') ?>">
                                    <i class="fas fa-cog"></i> 系统设置
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?= $this->url('logout') ?>">
                                    <i class="fas fa-sign-out-alt"></i> 退出登录
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 页面内容 -->
                <div class="content">
                    <?php if (isset($content)): ?>
                        <?= $content ?>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?= $this->asset('js/admin.js') ?>"></script>
    
    <?php if (isset($scripts)): ?>
        <?= $scripts ?>
    <?php endif; ?>
</body>
</html>
