<?php

namespace Controllers;

use Core\Controller;
use Models\Category;
use Models\Link;

/**
 * 管理员控制器
 */
class AdminController extends Controller
{
    /**
     * 管理后台首页
     */
    public function index()
    {
        $this->requireAdmin();
        
        // 获取统计数据
        $linkModel = new Link();
        $categoryModel = new Category();
        
        $stats = [
            'total_links' => $linkModel->count(),
            'active_links' => $linkModel->count('status = 1'),
            'total_categories' => $categoryModel->count(),
            'active_categories' => $categoryModel->count('status = 1'),
            'total_clicks' => $linkModel->fetch('SELECT SUM(click_count) as total FROM links')['total'] ?? 0
        ];
        
        // 获取最新链接
        $latestLinks = $linkModel->findAll('1=1', [], 'created_at DESC', '10');
        
        // 获取热门链接
        $popularLinks = $linkModel->findAll('1=1', [], 'click_count DESC', '10');
        
        $data = [
            'title' => '管理后台 - ' . SITE_NAME,
            'stats' => $stats,
            'latestLinks' => $latestLinks,
            'popularLinks' => $popularLinks
        ];
        
        $this->render('admin/dashboard', $data);
    }
    
    /**
     * 登录页面
     */
    public function login()
    {
        session_start();
        
        // 如果已登录，跳转到后台
        if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']) {
            $this->redirect('index.php?c=admin');
            return;
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $username = $this->sanitize($this->getPost('username'));
            $password = $this->getPost('password');
            
            // 验证登录信息
            if ($username === ADMIN_USERNAME && $password === ADMIN_PASSWORD) {
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_username'] = $username;
                $_SESSION['login_time'] = time();
                
                $this->json(['success' => true, 'message' => '登录成功']);
            } else {
                $this->json(['success' => false, 'message' => '用户名或密码错误']);
            }
            return;
        }
        
        $data = [
            'title' => '管理员登录 - ' . SITE_NAME
        ];
        
        $this->render('admin/login', $data);
    }
    
    /**
     * 退出登录
     */
    public function logout()
    {
        session_start();
        session_destroy();
        $this->redirect('index.php?c=login');
    }
    
    /**
     * 系统设置
     */
    public function settings()
    {
        $this->requireAdmin();
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // 处理设置保存
            $settings = [
                'site_title' => $this->sanitize($this->getPost('site_title')),
                'site_description' => $this->sanitize($this->getPost('site_description')),
                'theme' => $this->sanitize($this->getPost('theme')),
                'enable_registration' => intval($this->getPost('enable_registration')),
                'links_per_page' => intval($this->getPost('links_per_page'))
            ];
            
            // 保存设置到数据库
            foreach ($settings as $key => $value) {
                $existing = $this->db->fetch('SELECT id FROM settings WHERE setting_key = ?', [$key]);
                if ($existing) {
                    $this->db->update('settings', 
                        ['setting_value' => $value, 'updated_at' => date('Y-m-d H:i:s')], 
                        'setting_key = ?', [$key]);
                } else {
                    $this->db->insert('settings', [
                        'setting_key' => $key,
                        'setting_value' => $value,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                }
            }
            
            $this->json(['success' => true, 'message' => '设置保存成功']);
            return;
        }
        
        // 获取当前设置
        $settingsData = $this->db->fetchAll('SELECT setting_key, setting_value FROM settings');
        $settings = [];
        foreach ($settingsData as $setting) {
            $settings[$setting['setting_key']] = $setting['setting_value'];
        }
        
        $data = [
            'title' => '系统设置 - ' . SITE_NAME,
            'settings' => $settings
        ];
        
        $this->render('admin/settings', $data);
    }
    
    /**
     * 数据统计
     */
    public function statistics()
    {
        $this->requireAdmin();
        
        $linkModel = new Link();
        
        // 基础统计
        $basicStats = [
            'total_links' => $linkModel->count(),
            'active_links' => $linkModel->count('status = 1'),
            'total_clicks' => $linkModel->fetch('SELECT SUM(click_count) as total FROM links')['total'] ?? 0,
            'avg_clicks' => $linkModel->fetch('SELECT AVG(click_count) as avg FROM links WHERE status = 1')['avg'] ?? 0
        ];
        
        // 分类统计
        $categoryStats = $this->db->fetchAll('
            SELECT c.name, COUNT(l.id) as link_count, SUM(l.click_count) as total_clicks
            FROM categories c
            LEFT JOIN links l ON c.id = l.category_id AND l.status = 1
            WHERE c.status = 1
            GROUP BY c.id, c.name
            ORDER BY total_clicks DESC
        ');
        
        // 热门链接
        $topLinks = $linkModel->findAll('status = 1', [], 'click_count DESC', '20');
        
        // 最近点击统计（按天）
        $dailyStats = $this->db->fetchAll('
            SELECT DATE(click_time) as date, COUNT(*) as clicks
            FROM click_stats
            WHERE click_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY DATE(click_time)
            ORDER BY date DESC
        ');
        
        $data = [
            'title' => '数据统计 - ' . SITE_NAME,
            'basicStats' => $basicStats,
            'categoryStats' => $categoryStats,
            'topLinks' => $topLinks,
            'dailyStats' => $dailyStats
        ];
        
        $this->render('admin/statistics', $data);
    }
}
