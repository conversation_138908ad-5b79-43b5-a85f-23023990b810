/* WebStack主题样式 */

/* 全局变量 */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 15px;
    --box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* 基础样式重置 */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

/* 导航栏样式 */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link {
    color: var(--dark-color) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 25px;
    margin: 0 0.25rem;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    background: var(--primary-color);
    color: white !important;
}

/* 主容器 */
.main-container {
    padding-top: 100px;
    padding-bottom: 50px;
}

/* 搜索区域 */
.search-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 40px;
    margin-bottom: 40px;
    box-shadow: var(--box-shadow);
    text-align: center;
}

.search-tabs .nav-link {
    border: none;
    background: transparent;
    color: var(--secondary-color);
    font-weight: 500;
    padding: 10px 20px;
    border-radius: 25px;
    margin-right: 10px;
    transition: var(--transition);
}

.search-tabs .nav-link.active {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.search-input-group {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.search-input {
    border: 2px solid #e9ecef;
    border-radius: 50px;
    padding: 15px 60px 15px 25px;
    font-size: 16px;
    width: 100%;
    transition: var(--transition);
    box-shadow: none;
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

.search-btn {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    border: none;
    background: var(--primary-color);
    color: white;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    transition: var(--transition);
    cursor: pointer;
}

.search-btn:hover {
    background: #0056b3;
    transform: translateY(-50%) scale(1.05);
}

/* 分类区域 */
.category-section {
    margin-bottom: 40px;
}

.category-title {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 20px 30px;
    margin-bottom: 20px;
    box-shadow: var(--box-shadow);
    border-left: 5px solid var(--primary-color);
}

.category-title h3 {
    margin: 0;
    color: var(--dark-color);
    font-weight: 600;
    display: flex;
    align-items: center;
}

.category-title h3 i {
    margin-right: 15px;
    color: var(--primary-color);
}

/* 链接网格 */
.links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

.link-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 20px;
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--box-shadow);
    display: block;
}

.link-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: inherit;
}

.link-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--light-color);
    overflow: hidden;
}

.link-icon img {
    width: 32px;
    height: 32px;
    object-fit: cover;
}

.link-icon i {
    font-size: 24px;
    color: var(--primary-color);
}

.link-title {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 8px;
    color: var(--dark-color);
}

.link-description {
    font-size: 14px;
    color: var(--secondary-color);
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.link-meta {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: var(--secondary-color);
}

.click-count {
    background: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
}

/* 新闻区域 */
.news-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--box-shadow);
}

.news-title {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.news-title i {
    margin-right: 10px;
    color: var(--danger-color);
}

.news-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.news-item {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
}

.news-item:last-child {
    border-bottom: none;
}

.news-index {
    background: var(--primary-color);
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    margin-right: 10px;
    flex-shrink: 0;
}

.news-link {
    color: var(--dark-color);
    text-decoration: none;
    font-size: 14px;
    line-height: 1.4;
    flex: 1;
}

.news-link:hover {
    color: var(--primary-color);
    text-decoration: none;
}

/* 页脚 */
.footer {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 30px;
    margin-top: 50px;
    text-align: center;
    box-shadow: var(--box-shadow);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-container {
        padding-top: 80px;
    }
    
    .search-section {
        padding: 25px;
        margin-bottom: 25px;
    }
    
    .links-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .category-title {
        padding: 15px 20px;
    }
    
    .navbar-nav {
        text-align: center;
    }
    
    .navbar-nav .nav-link {
        margin: 0.25rem 0;
    }
}

@media (max-width: 576px) {
    .search-input {
        padding: 12px 50px 12px 20px;
        font-size: 14px;
    }
    
    .search-btn {
        width: 40px;
        height: 40px;
    }
    
    .link-card {
        padding: 15px;
    }
    
    .category-title h3 {
        font-size: 1.1rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.link-card {
    animation: fadeInUp 0.6s ease-out;
}

.category-section:nth-child(even) .link-card {
    animation-delay: 0.1s;
}

.category-section:nth-child(odd) .link-card {
    animation-delay: 0.2s;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}
