/* 管理后台样式 */

/* 全局变量 */
:root {
    --admin-primary: #007bff;
    --admin-secondary: #6c757d;
    --admin-success: #28a745;
    --admin-info: #17a2b8;
    --admin-warning: #ffc107;
    --admin-danger: #dc3545;
    --admin-light: #f8f9fa;
    --admin-dark: #343a40;
    --sidebar-bg: #2c3e50;
    --sidebar-hover: #34495e;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.15s ease-in-out;
}

/* 基础样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--admin-dark);
    background-color: var(--admin-light);
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);
    background-color: var(--sidebar-bg);
    color: white;
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: 0.5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    color: #bdc3c7;
    padding: 0.75rem 1rem;
    border-radius: 0;
    transition: var(--transition);
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: var(--sidebar-hover);
    color: white;
}

.sidebar .nav-link i {
    width: 20px;
    margin-right: 10px;
    text-align: center;
}

.sidebar-heading {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: #95a5a6;
    font-weight: 600;
}

/* 主内容区域 */
.main-content {
    margin-left: 240px;
    padding: 20px;
    background-color: var(--admin-light);
    min-height: 100vh;
}

/* 顶部导航 */
.navbar-brand {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    font-size: 1rem;
    background-color: rgba(0, 0, 0, 0.25);
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);
}

.navbar .form-control {
    padding: 0.375rem 0.75rem;
    border-width: 0;
    border-radius: 0;
}

.form-control-dark {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.1);
}

.form-control-dark:focus {
    border-color: transparent;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.25);
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 0.75rem 1.25rem;
    font-weight: 600;
}

.card-body {
    padding: 1.25rem;
}

/* 统计卡片 */
.stats-card {
    background: linear-gradient(135deg, var(--admin-primary), #0056b3);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stats-card.success {
    background: linear-gradient(135deg, var(--admin-success), #1e7e34);
}

.stats-card.info {
    background: linear-gradient(135deg, var(--admin-info), #117a8b);
}

.stats-card.warning {
    background: linear-gradient(135deg, var(--admin-warning), #d39e00);
}

.stats-card.danger {
    background: linear-gradient(135deg, var(--admin-danger), #bd2130);
}

.stats-card h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card p {
    margin-bottom: 0;
    opacity: 0.9;
}

.stats-card i {
    font-size: 2.5rem;
    opacity: 0.3;
    float: right;
    margin-top: -10px;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--admin-dark);
    background-color: var(--admin-light);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

/* 按钮样式 */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* 表单样式 */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--admin-dark);
}

/* 徽章样式 */
.badge {
    font-weight: 500;
}

/* 分页样式 */
.pagination {
    margin-bottom: 0;
}

.page-link {
    border-radius: var(--border-radius);
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: var(--admin-primary);
}

.page-link:hover {
    background-color: var(--admin-light);
    border-color: var(--admin-primary);
}

.page-item.active .page-link {
    background-color: var(--admin-primary);
    border-color: var(--admin-primary);
}

/* 模态框样式 */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

/* 下拉菜单样式 */
.dropdown-menu {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: var(--admin-light);
}

/* 进度条样式 */
.progress {
    border-radius: var(--border-radius);
    height: 0.5rem;
}

.progress-bar {
    border-radius: var(--border-radius);
}

/* 警告框样式 */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 0.75rem 1rem;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.75rem;
}

.tooltip-inner {
    border-radius: var(--border-radius);
}

/* 响应式设计 */
@media (max-width: 767.98px) {
    .sidebar {
        top: 5rem;
        width: 100%;
        height: auto;
        position: static;
    }
    
    .main-content {
        margin-left: 0;
        padding: 1rem;
    }
    
    .stats-card {
        text-align: center;
    }
    
    .stats-card i {
        float: none;
        display: block;
        margin: 0 auto 1rem;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 主题切换动画 */
.theme-transition {
    transition: all 0.3s ease;
}

/* 图标对齐 */
.icon-align {
    display: inline-flex;
    align-items: center;
}

.icon-align i {
    margin-right: 0.5rem;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.online {
    background-color: var(--admin-success);
}

.status-indicator.offline {
    background-color: var(--admin-danger);
}

.status-indicator.away {
    background-color: var(--admin-warning);
}

/* 文件上传区域 */
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--admin-primary);
    background-color: rgba(0, 123, 255, 0.05);
}

.upload-area.dragover {
    border-color: var(--admin-success);
    background-color: rgba(40, 167, 69, 0.05);
}

/* 代码块样式 */
.code-block {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    overflow-x: auto;
}

/* 标签输入 */
.tag-input {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    padding: 0.375rem 0.75rem;
    border: 1px solid #ced4da;
    border-radius: var(--border-radius);
    min-height: calc(1.5em + 0.75rem + 2px);
}

.tag {
    background-color: var(--admin-primary);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.tag .remove {
    cursor: pointer;
    font-weight: bold;
}

.tag .remove:hover {
    color: #ffcccc;
}
