<?php

namespace Core;

/**
 * 模板引擎类
 */
class Template
{
    private $templatePath;
    private $data = [];
    
    public function __construct()
    {
        $this->templatePath = TEMPLATE_PATH;
    }
    
    /**
     * 设置模板变量
     */
    public function assign($key, $value)
    {
        $this->data[$key] = $value;
    }
    
    /**
     * 渲染模板
     */
    public function render($template, $data = [])
    {
        // 合并数据
        $this->data = array_merge($this->data, $data);
        
        // 提取变量到当前作用域
        extract($this->data);
        
        // 包含模板文件
        $templateFile = $this->templatePath . '/' . $template . '.php';
        
        if (file_exists($templateFile)) {
            include $templateFile;
        } else {
            throw new \Exception("Template file not found: {$templateFile}");
        }
    }
    
    /**
     * 包含子模板
     */
    public function include($template, $data = [])
    {
        $templateFile = $this->templatePath . '/' . $template . '.php';
        
        if (file_exists($templateFile)) {
            // 提取变量
            extract(array_merge($this->data, $data));
            include $templateFile;
        }
    }
    
    /**
     * 转义输出
     */
    public function escape($string)
    {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * 格式化日期
     */
    public function formatDate($date, $format = 'Y-m-d H:i:s')
    {
        if (empty($date)) {
            return '';
        }
        
        if (is_string($date)) {
            $date = strtotime($date);
        }
        
        return date($format, $date);
    }
    
    /**
     * 截取字符串
     */
    public function truncate($string, $length = 100, $suffix = '...')
    {
        if (mb_strlen($string, 'UTF-8') <= $length) {
            return $string;
        }
        
        return mb_substr($string, 0, $length, 'UTF-8') . $suffix;
    }
    
    /**
     * 生成URL
     */
    public function url($controller = '', $action = 'index', $params = [])
    {
        return Router::url($controller, $action, $params);
    }
    
    /**
     * 获取静态资源URL
     */
    public function asset($path)
    {
        return '/static/' . ltrim($path, '/');
    }
    
    /**
     * 检查是否为当前页面
     */
    public function isActive($controller, $action = '')
    {
        $currentController = $_GET['c'] ?? '';
        $currentAction = $_GET['action'] ?? 'index';
        
        if ($controller !== $currentController) {
            return false;
        }
        
        if (!empty($action) && $action !== $currentAction) {
            return false;
        }
        
        return true;
    }
}
