<?php
$content = ob_start();
?>

<div class="container mt-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="text-center">
                <h1 class="display-4 mb-3"><?= SITE_NAME ?></h1>
                <p class="lead text-muted"><?= SITE_DESCRIPTION ?></p>
            </div>
        </div>
    </div>

    <!-- 统计信息 -->
    <?php if (isset($stats) && $stats): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h3><?= $stats['total_links'] ?? 0 ?></h3>
                            <small>收录网站</small>
                        </div>
                        <div class="col-md-3">
                            <h3><?= $stats['active_links'] ?? 0 ?></h3>
                            <small>活跃网站</small>
                        </div>
                        <div class="col-md-3">
                            <h3><?= $stats['total_clicks'] ?? 0 ?></h3>
                            <small>总点击数</small>
                        </div>
                        <div class="col-md-3">
                            <h3><?= number_format($stats['avg_clicks'] ?? 0, 1) ?></h3>
                            <small>平均点击</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- 网站分类和链接 -->
    <?php if (!empty($categoriesWithLinks)): ?>
        <?php foreach ($categoriesWithLinks as $categoryData): ?>
            <?php $category = $categoryData['category']; ?>
            <?php $links = $categoryData['links']; ?>
            
            <div class="category-section mb-5">
                <div class="row">
                    <div class="col-12">
                        <h3 class="category-title mb-3">
                            <i class="fas fa-folder-open me-2"></i>
                            <?= $this->escape($category['name']) ?>
                        </h3>
                    </div>
                </div>
                
                <div class="row">
                    <?php foreach ($links as $link): ?>
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                        <div class="card link-card h-100" data-link-id="<?= $link['id'] ?>">
                            <div class="card-body d-flex flex-column">
                                <div class="d-flex align-items-center mb-2">
                                    <?php if (!empty($link['icon'])): ?>
                                    <img src="<?= $this->escape($link['icon']) ?>" 
                                         alt="<?= $this->escape($link['title']) ?>" 
                                         class="link-icon me-2"
                                         onerror="this.src='<?= $this->asset('images/default-icon.png') ?>'">
                                    <?php else: ?>
                                    <i class="fas fa-link link-icon me-2"></i>
                                    <?php endif; ?>
                                    <h6 class="card-title mb-0 flex-grow-1">
                                        <?= $this->escape($link['title']) ?>
                                    </h6>
                                </div>
                                
                                <?php if (!empty($link['description'])): ?>
                                <p class="card-text text-muted small flex-grow-1">
                                    <?= $this->escape($this->truncate($link['description'], 80)) ?>
                                </p>
                                <?php endif; ?>
                                
                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-mouse-pointer"></i>
                                            <?= $link['click_count'] ?? 0 ?>
                                        </small>
                                        <a href="<?= $this->url('link', 'go', ['id' => $link['id']]) ?>" 
                                           class="btn btn-sm btn-outline-primary"
                                           target="_blank"
                                           onclick="recordClick(<?= $link['id'] ?>)">
                                            访问 <i class="fas fa-external-link-alt"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="row">
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">暂无收录网站</h4>
                    <p class="text-muted">管理员还没有添加任何网站链接</p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- 热门和最新链接 -->
    <?php if (!empty($popularLinks) || !empty($latestLinks)): ?>
    <div class="row mt-5">
        <?php if (!empty($popularLinks)): ?>
        <div class="col-md-6">
            <h4 class="mb-3"><i class="fas fa-fire text-danger me-2"></i>热门网站</h4>
            <div class="list-group">
                <?php foreach ($popularLinks as $link): ?>
                <a href="<?= $this->url('link', 'go', ['id' => $link['id']]) ?>" 
                   class="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                   target="_blank">
                    <div class="d-flex align-items-center">
                        <?php if (!empty($link['icon'])): ?>
                        <img src="<?= $this->escape($link['icon']) ?>" 
                             alt="<?= $this->escape($link['title']) ?>" 
                             class="link-icon-small me-2">
                        <?php endif; ?>
                        <span><?= $this->escape($link['title']) ?></span>
                    </div>
                    <span class="badge bg-primary rounded-pill"><?= $link['click_count'] ?></span>
                </a>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($latestLinks)): ?>
        <div class="col-md-6">
            <h4 class="mb-3"><i class="fas fa-clock text-success me-2"></i>最新收录</h4>
            <div class="list-group">
                <?php foreach ($latestLinks as $link): ?>
                <a href="<?= $this->url('link', 'go', ['id' => $link['id']]) ?>" 
                   class="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                   target="_blank">
                    <div class="d-flex align-items-center">
                        <?php if (!empty($link['icon'])): ?>
                        <img src="<?= $this->escape($link['icon']) ?>" 
                             alt="<?= $this->escape($link['title']) ?>" 
                             class="link-icon-small me-2">
                        <?php endif; ?>
                        <span><?= $this->escape($link['title']) ?></span>
                    </div>
                    <small class="text-muted"><?= $this->formatDate($link['created_at'], 'm-d') ?></small>
                </a>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>
</div>

<?php
$content = ob_get_clean();
include TEMPLATE_PATH . '/layout.php';
?>
