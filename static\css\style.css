/* NavStack 自定义样式 */

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.15s ease-in-out;
}

/* 深色主题 */
[data-theme="dark"] {
    --bs-body-bg: #1a1a1a;
    --bs-body-color: #e9ecef;
    --bs-card-bg: #2d3748;
    --bs-border-color: #4a5568;
}

/* 全局样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    transition: var(--transition);
}

.main-content {
    min-height: calc(100vh - 200px);
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar .form-control {
    border: 1px solid rgba(255, 255, 255, 0.2);
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.navbar .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.navbar .form-control:focus {
    border-color: rgba(255, 255, 255, 0.4);
    background-color: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

/* 卡片样式 */
.link-card {
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
}

.link-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.link-card .card-body {
    padding: 1rem;
}

.link-card .card-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.link-card .card-text {
    font-size: 0.85rem;
    line-height: 1.4;
}

/* 图标样式 */
.link-icon {
    width: 20px;
    height: 20px;
    object-fit: contain;
    border-radius: 3px;
}

.link-icon-small {
    width: 16px;
    height: 16px;
    object-fit: contain;
    border-radius: 2px;
}

/* 分类标题 */
.category-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-color);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}

/* 搜索结果 */
.search-results .card {
    margin-bottom: 1rem;
}

.search-highlight {
    background-color: #fff3cd;
    padding: 0.1rem 0.2rem;
    border-radius: 0.2rem;
}

/* 统计卡片 */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border: none;
}

.stats-card .card-body {
    padding: 2rem;
}

.stats-card h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

/* 页脚 */
.footer {
    margin-top: auto;
    border-top: 1px solid #e9ecef;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.1rem;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .category-title {
        font-size: 1.25rem;
    }
    
    .link-card .card-body {
        padding: 0.75rem;
    }
    
    .stats-card h3 {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .link-card {
        margin-bottom: 1rem;
    }
    
    .navbar .input-group {
        width: 100%;
        margin-top: 0.5rem;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 深色主题样式 */
[data-theme="dark"] {
    background-color: #1a1a1a;
    color: #e9ecef;
}

[data-theme="dark"] .card {
    background-color: #2d3748;
    border-color: #4a5568;
}

[data-theme="dark"] .link-card .card-title {
    color: #e9ecef;
}

[data-theme="dark"] .category-title {
    color: #e9ecef;
}

[data-theme="dark"] .footer {
    background-color: #2d3748 !important;
    border-color: #4a5568;
}

[data-theme="dark"] .list-group-item {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e9ecef;
}

[data-theme="dark"] .list-group-item:hover {
    background-color: #4a5568;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 工具提示 */
.tooltip {
    font-size: 0.875rem;
}

/* 按钮样式 */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.btn:hover {
    transform: translateY(-1px);
}

/* 表单样式 */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
