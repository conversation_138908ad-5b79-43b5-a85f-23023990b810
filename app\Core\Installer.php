<?php

namespace Core;

/**
 * 安装器类
 */
class Installer
{
    private $db;
    
    public function __construct()
    {
        $this->db = Database::getInstance();
    }
    
    /**
     * 执行安装
     */
    public function install()
    {
        try {
            // 检查是否需要重新安装
            $this->checkAndCleanDatabase();

            // 创建数据表
            $this->createTables();

            // 插入默认数据
            $this->insertDefaultData();

            return true;
        } catch (Exception $e) {
            throw new Exception('安装失败: ' . $e->getMessage());
        }
    }

    /**
     * 检查并清理数据库
     */
    private function checkAndCleanDatabase()
    {
        try {
            // 检查用户表是否存在且有数据
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM sqlite_master WHERE type='table' AND name='users'");

            if ($result && $result['count'] > 0) {
                // 表存在，检查是否有数据
                $userCount = $this->db->fetch("SELECT COUNT(*) as count FROM users");

                if ($userCount && $userCount['count'] > 0) {
                    // 有数据，清理所有表
                    $this->dropAllTables();
                }
            }
        } catch (Exception $e) {
            // 如果出错，可能是表不存在，继续安装
        }
    }

    /**
     * 删除所有表
     */
    private function dropAllTables()
    {
        $tables = [
            'click_stats',
            'friend_links',
            'navigation_menus',
            'advertisements',
            'submissions',
            'guestbook',
            'articles',
            'themes',
            'links',
            'categories',
            'users',
            'user_groups',
            'settings'
        ];

        foreach ($tables as $table) {
            try {
                $this->db->query("DROP TABLE IF EXISTS {$table}");
            } catch (Exception $e) {
                // 忽略删除失败的错误
            }
        }
    }
    
    /**
     * 创建数据表
     */
    private function createTables()
    {
        // 用户表
        $sql = "CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            email VARCHAR(100) DEFAULT '',
            nickname VARCHAR(100) DEFAULT '',
            avatar VARCHAR(200) DEFAULT '',
            group_id INTEGER DEFAULT 1,
            status INTEGER DEFAULT 1,
            last_login_time DATETIME DEFAULT NULL,
            last_login_ip VARCHAR(45) DEFAULT '',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
        $this->db->query($sql);

        // 用户组表
        $sql = "CREATE TABLE IF NOT EXISTS user_groups (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(50) NOT NULL,
            description TEXT DEFAULT '',
            permissions TEXT DEFAULT '',
            status INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
        $this->db->query($sql);

        // 分类表（扩展）
        $sql = "CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            parent_id INTEGER DEFAULT 0,
            sort_order INTEGER DEFAULT 0,
            icon VARCHAR(100) DEFAULT '',
            description TEXT DEFAULT '',
            password VARCHAR(100) DEFAULT '',
            is_private INTEGER DEFAULT 0,
            user_id INTEGER DEFAULT 0,
            status INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
        $this->db->query($sql);

        // 链接表（扩展）
        $sql = "CREATE TABLE IF NOT EXISTS links (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title VARCHAR(200) NOT NULL,
            url VARCHAR(500) NOT NULL,
            description TEXT DEFAULT '',
            icon VARCHAR(200) DEFAULT '',
            category_id INTEGER DEFAULT 0,
            sort_order INTEGER DEFAULT 0,
            password VARCHAR(100) DEFAULT '',
            is_private INTEGER DEFAULT 0,
            user_id INTEGER DEFAULT 0,
            status INTEGER DEFAULT 1,
            click_count INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
        $this->db->query($sql);

        // 点击统计表
        $sql = "CREATE TABLE IF NOT EXISTS click_stats (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            link_id INTEGER NOT NULL,
            ip_address VARCHAR(45) DEFAULT '',
            user_agent TEXT DEFAULT '',
            referer VARCHAR(500) DEFAULT '',
            click_time DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
        $this->db->query($sql);

        // 主题表
        $sql = "CREATE TABLE IF NOT EXISTS themes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(50) NOT NULL,
            title VARCHAR(100) NOT NULL,
            description TEXT DEFAULT '',
            version VARCHAR(20) DEFAULT '1.0.0',
            author VARCHAR(50) DEFAULT '',
            preview_image VARCHAR(200) DEFAULT '',
            is_active INTEGER DEFAULT 0,
            config TEXT DEFAULT '',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
        $this->db->query($sql);

        // 文章表
        $sql = "CREATE TABLE IF NOT EXISTS articles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title VARCHAR(200) NOT NULL,
            content TEXT DEFAULT '',
            excerpt TEXT DEFAULT '',
            cover_image VARCHAR(200) DEFAULT '',
            author_id INTEGER DEFAULT 0,
            category_id INTEGER DEFAULT 0,
            tags VARCHAR(200) DEFAULT '',
            view_count INTEGER DEFAULT 0,
            status INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
        $this->db->query($sql);

        // 留言表
        $sql = "CREATE TABLE IF NOT EXISTS guestbook (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(50) NOT NULL,
            email VARCHAR(100) DEFAULT '',
            website VARCHAR(200) DEFAULT '',
            content TEXT NOT NULL,
            reply TEXT DEFAULT '',
            ip_address VARCHAR(45) DEFAULT '',
            status INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            replied_at DATETIME DEFAULT NULL
        )";
        $this->db->query($sql);

        // 收录申请表
        $sql = "CREATE TABLE IF NOT EXISTS submissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            site_name VARCHAR(100) NOT NULL,
            site_url VARCHAR(500) NOT NULL,
            site_description TEXT DEFAULT '',
            site_icon VARCHAR(200) DEFAULT '',
            category_id INTEGER DEFAULT 0,
            contact_email VARCHAR(100) DEFAULT '',
            contact_qq VARCHAR(20) DEFAULT '',
            status INTEGER DEFAULT 0,
            admin_note TEXT DEFAULT '',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            processed_at DATETIME DEFAULT NULL
        )";
        $this->db->query($sql);

        // 广告表
        $sql = "CREATE TABLE IF NOT EXISTS advertisements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title VARCHAR(100) NOT NULL,
            content TEXT DEFAULT '',
            image VARCHAR(200) DEFAULT '',
            link VARCHAR(500) DEFAULT '',
            position VARCHAR(50) DEFAULT '',
            sort_order INTEGER DEFAULT 0,
            start_time DATETIME DEFAULT NULL,
            end_time DATETIME DEFAULT NULL,
            status INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
        $this->db->query($sql);

        // 友情链接表
        $sql = "CREATE TABLE IF NOT EXISTS friend_links (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            url VARCHAR(500) NOT NULL,
            description TEXT DEFAULT '',
            logo VARCHAR(200) DEFAULT '',
            sort_order INTEGER DEFAULT 0,
            status INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
        $this->db->query($sql);

        // 导航菜单表
        $sql = "CREATE TABLE IF NOT EXISTS navigation_menus (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            url VARCHAR(500) NOT NULL,
            icon VARCHAR(100) DEFAULT '',
            parent_id INTEGER DEFAULT 0,
            sort_order INTEGER DEFAULT 0,
            target VARCHAR(20) DEFAULT '_self',
            status INTEGER DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
        $this->db->query($sql);

        // 系统设置表（扩展）
        $sql = "CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            setting_key VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT DEFAULT '',
            setting_type VARCHAR(20) DEFAULT 'text',
            setting_group VARCHAR(50) DEFAULT 'general',
            description TEXT DEFAULT '',
            sort_order INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
        $this->db->query($sql);
    }
    
    /**
     * 插入默认数据
     */
    private function insertDefaultData()
    {
        // 默认用户组
        $userGroups = [
            ['name' => '管理员', 'description' => '系统管理员，拥有所有权限', 'permissions' => 'all'],
            ['name' => '编辑', 'description' => '内容编辑，可管理链接和文章', 'permissions' => 'edit'],
            ['name' => '用户', 'description' => '普通用户，只能查看公开内容', 'permissions' => 'view']
        ];

        foreach ($userGroups as $group) {
            $this->db->insert('user_groups', $group);
        }

        // 默认管理员用户
        $adminUser = [
            'username' => ADMIN_USERNAME,
            'password' => password_hash(ADMIN_PASSWORD, PASSWORD_DEFAULT),
            'email' => ADMIN_EMAIL,
            'nickname' => '系统管理员',
            'group_id' => 1,
            'status' => 1
        ];
        $this->db->insert('users', $adminUser);

        // 默认分类
        $categories = [
            ['name' => '主题预览', 'icon' => 'fas fa-palette', 'sort_order' => 1],
            ['name' => '开发相关', 'icon' => 'fas fa-code', 'sort_order' => 2],
            ['name' => '影视资源', 'icon' => 'fas fa-video', 'sort_order' => 3],
            ['name' => '云盘储存', 'icon' => 'fas fa-cloud', 'sort_order' => 4],
            ['name' => '云服务器', 'icon' => 'fas fa-server', 'sort_order' => 5],
            ['name' => '精选文章', 'icon' => 'fas fa-book', 'sort_order' => 6]
        ];

        foreach ($categories as $category) {
            $this->db->insert('categories', $category);
        }
        
        // 默认主题
        $themes = [
            [
                'name' => 'default',
                'title' => '默认主题',
                'description' => '系统默认主题，简洁美观',
                'version' => '1.0.0',
                'author' => 'NavStack',
                'is_active' => 1
            ],
            [
                'name' => 'webstack',
                'title' => 'WebStack主题',
                'description' => '仿WebStack风格主题',
                'version' => '1.0.0',
                'author' => 'NavStack',
                'is_active' => 0
            ]
        ];

        foreach ($themes as $theme) {
            $this->db->insert('themes', $theme);
        }

        // 默认链接
        $links = [
            [
                'title' => 'TwoNav演示站',
                'url' => 'http://two.lm21.top',
                'description' => 'TwoNav官方演示站点',
                'icon' => 'https://two.lm21.top/favicon.ico',
                'category_id' => 1,
                'sort_order' => 1,
                'user_id' => 1
            ],
            [
                'title' => 'GitHub',
                'url' => 'https://github.com',
                'description' => '全球最大的代码托管平台',
                'icon' => 'https://github.com/favicon.ico',
                'category_id' => 2,
                'sort_order' => 1,
                'user_id' => 1
            ],
            [
                'title' => 'Vue.js',
                'url' => 'https://vuejs.org',
                'description' => '渐进式JavaScript框架',
                'icon' => 'https://vuejs.org/favicon.ico',
                'category_id' => 2,
                'sort_order' => 2,
                'user_id' => 1
            ]
        ];

        foreach ($links as $link) {
            $this->db->insert('links', $link);
        }

        // 默认导航菜单
        $menus = [
            ['name' => '主题预览', 'url' => '#category-1', 'icon' => 'fas fa-palette', 'sort_order' => 1],
            ['name' => '开发相关', 'url' => '#category-2', 'icon' => 'fas fa-code', 'sort_order' => 2],
            ['name' => '影视资源', 'url' => '#category-3', 'icon' => 'fas fa-video', 'sort_order' => 3],
            ['name' => '云盘储存', 'url' => '#category-4', 'icon' => 'fas fa-cloud', 'sort_order' => 4],
            ['name' => '云服务器', 'url' => '#category-5', 'icon' => 'fas fa-server', 'sort_order' => 5],
            ['name' => '精选文章', 'url' => '#category-6', 'icon' => 'fas fa-book', 'sort_order' => 6]
        ];

        foreach ($menus as $menu) {
            $this->db->insert('navigation_menus', $menu);
        }

        // 默认设置
        $settings = [
            ['setting_key' => 'site_title', 'setting_value' => SITE_NAME, 'setting_group' => 'general'],
            ['setting_key' => 'site_description', 'setting_value' => SITE_DESCRIPTION, 'setting_group' => 'general'],
            ['setting_key' => 'site_keywords', 'setting_value' => '导航,网址,书签,工具', 'setting_group' => 'general'],
            ['setting_key' => 'site_logo', 'setting_value' => '', 'setting_group' => 'general'],
            ['setting_key' => 'active_theme', 'setting_value' => 'default', 'setting_group' => 'theme'],
            ['setting_key' => 'enable_registration', 'setting_value' => '0', 'setting_group' => 'user'],
            ['setting_key' => 'enable_guestbook', 'setting_value' => '1', 'setting_group' => 'feature'],
            ['setting_key' => 'enable_submission', 'setting_value' => '1', 'setting_group' => 'feature'],
            ['setting_key' => 'enable_news', 'setting_value' => '1', 'setting_group' => 'feature'],
            ['setting_key' => 'links_per_page', 'setting_value' => '20', 'setting_group' => 'general'],
            ['setting_key' => 'search_engines', 'setting_value' => 'baidu,bing,google', 'setting_group' => 'search'],
            ['setting_key' => 'default_search', 'setting_value' => 'baidu', 'setting_group' => 'search']
        ];

        foreach ($settings as $setting) {
            $this->db->insert('settings', $setting);
        }
    }
}
