<?php

namespace Controllers;

use Core\Controller;
use Models\Theme;

/**
 * 主题管理控制器
 */
class ThemeController extends Controller
{
    private $themeModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->themeModel = new Theme();
    }
    
    /**
     * 主题管理首页
     */
    public function index()
    {
        $this->requireAdmin();
        
        // 获取所有主题
        $themes = $this->themeModel->getAllThemes();
        
        // 获取当前激活主题
        $activeTheme = $this->themeModel->getActiveTheme();
        
        // 获取预设主题
        $presetThemes = $this->themeModel->getThemePresets();
        
        $data = [
            'title' => '主题管理 - ' . SITE_NAME,
            'themes' => $themes,
            'activeTheme' => $activeTheme,
            'presetThemes' => $presetThemes
        ];
        
        $this->render('admin/themes/index', $data);
    }
    
    /**
     * 激活主题
     */
    public function activate()
    {
        $this->requireAdmin();
        
        $themeId = $this->getPost('theme_id');
        if (!$themeId) {
            $this->json(['success' => false, 'message' => '主题ID不能为空']);
            return;
        }
        
        $theme = $this->themeModel->findById($themeId);
        if (!$theme) {
            $this->json(['success' => false, 'message' => '主题不存在']);
            return;
        }
        
        // 检查主题文件是否存在
        if (!$this->themeModel->themeFileExists($theme['name'])) {
            $this->json(['success' => false, 'message' => '主题文件不存在']);
            return;
        }
        
        if ($this->themeModel->activateTheme($themeId)) {
            // 更新系统设置
            $this->db->update('settings', 
                ['setting_value' => $theme['name']], 
                'setting_key = ?', ['active_theme']);
            
            $this->json(['success' => true, 'message' => '主题激活成功']);
        } else {
            $this->json(['success' => false, 'message' => '主题激活失败']);
        }
    }
    
    /**
     * 安装预设主题
     */
    public function installPreset()
    {
        $this->requireAdmin();
        
        $themeName = $this->getPost('theme_name');
        if (!$themeName) {
            $this->json(['success' => false, 'message' => '主题名称不能为空']);
            return;
        }
        
        $presets = $this->themeModel->getThemePresets();
        if (!isset($presets[$themeName])) {
            $this->json(['success' => false, 'message' => '主题不存在']);
            return;
        }
        
        $themeData = $presets[$themeName];
        
        // 检查是否已安装
        if ($this->themeModel->getThemeByName($themeName)) {
            $this->json(['success' => false, 'message' => '主题已安装']);
            return;
        }
        
        if ($this->themeModel->create($themeData)) {
            $this->json(['success' => true, 'message' => '主题安装成功']);
        } else {
            $this->json(['success' => false, 'message' => '主题安装失败']);
        }
    }
    
    /**
     * 卸载主题
     */
    public function uninstall()
    {
        $this->requireAdmin();
        
        $themeId = $this->getPost('theme_id');
        if (!$themeId) {
            $this->json(['success' => false, 'message' => '主题ID不能为空']);
            return;
        }
        
        if ($this->themeModel->uninstallTheme($themeId)) {
            $this->json(['success' => true, 'message' => '主题卸载成功']);
        } else {
            $this->json(['success' => false, 'message' => '主题卸载失败或主题正在使用中']);
        }
    }
    
    /**
     * 主题配置
     */
    public function config($themeId = null)
    {
        $this->requireAdmin();
        
        if (!$themeId) {
            $this->redirect('index.php?c=theme');
            return;
        }
        
        $theme = $this->themeModel->findById($themeId);
        if (!$theme) {
            $this->redirect('index.php?c=theme');
            return;
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $config = $this->getPost('config', []);
            
            if ($this->themeModel->updateThemeConfig($themeId, $config)) {
                $this->json(['success' => true, 'message' => '配置保存成功']);
            } else {
                $this->json(['success' => false, 'message' => '配置保存失败']);
            }
            return;
        }
        
        $config = $this->themeModel->getThemeConfig($themeId);
        
        $data = [
            'title' => '主题配置 - ' . $theme['title'],
            'theme' => $theme,
            'config' => $config
        ];
        
        $this->render('admin/themes/config', $data);
    }
    
    /**
     * 主题预览
     */
    public function preview($themeName = null)
    {
        if (!$themeName) {
            $this->redirect('/');
            return;
        }
        
        $theme = $this->themeModel->getThemeByName($themeName);
        if (!$theme) {
            $this->redirect('/');
            return;
        }
        
        // 临时设置主题进行预览
        $_SESSION['preview_theme'] = $themeName;
        
        $this->redirect('/');
    }
    
    /**
     * 批量安装预设主题
     */
    public function installAll()
    {
        $this->requireAdmin();
        
        $installed = $this->themeModel->installPresetThemes();
        
        $this->json([
            'success' => true, 
            'message' => "成功安装 {$installed} 个主题"
        ]);
    }
}
