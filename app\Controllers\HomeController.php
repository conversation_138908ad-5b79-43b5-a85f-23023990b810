<?php

namespace Controllers;

use Core\Controller;
use Models\Category;
use Models\Link;

/**
 * 首页控制器
 */
class HomeController extends Controller
{
    private $categoryModel;
    private $linkModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->categoryModel = new Category();
        $this->linkModel = new Link();
    }
    
    /**
     * 首页
     */
    public function index()
    {
        // 获取分类和链接
        $categoriesWithLinks = $this->linkModel->getLinksByCategory();

        // 获取所有分类（用于导航）
        $categories = $this->categoryModel->findAll('status = 1', [], 'sort_order ASC, id ASC');

        // 获取统计信息
        $stats = $this->linkModel->getStats();

        // 获取热门链接
        $popularLinks = $this->linkModel->getPopularLinks(8);

        // 获取最新链接
        $latestLinks = $this->linkModel->getLatestLinks(8);

        // 获取热点新闻（模拟数据）
        $hotNews = $this->getHotNews();

        // 获取百度热搜（模拟数据）
        $baiduHot = $this->getBaiduHot();

        // 获取当前主题
        $currentTheme = $this->getCurrentTheme();

        $data = [
            'title' => SITE_NAME,
            'description' => SITE_DESCRIPTION,
            'categories' => $categories,
            'categoriesWithLinks' => $categoriesWithLinks,
            'stats' => $stats,
            'popularLinks' => $popularLinks,
            'latestLinks' => $latestLinks,
            'hotNews' => $hotNews,
            'baiduHot' => $baiduHot
        ];

        // 根据主题选择模板
        $template = $currentTheme === 'webstack' ? 'themes/webstack/index' : 'home/index';
        $this->render($template, $data);
    }
    
    /**
     * 搜索
     */
    public function search()
    {
        $keyword = $this->getGet('q', '');
        $keyword = trim($keyword);
        
        $results = [];
        if (!empty($keyword)) {
            $results = $this->linkModel->searchLinks($keyword);
        }
        
        $data = [
            'title' => '搜索结果 - ' . SITE_NAME,
            'keyword' => $keyword,
            'results' => $results,
            'total' => count($results)
        ];
        
        $this->render('home/search', $data);
    }
    
    /**
     * 分类页面
     */
    public function category($categoryId = null)
    {
        if (!$categoryId) {
            $this->redirect('/');
            return;
        }
        
        // 获取分类信息
        $category = $this->categoryModel->findById($categoryId);
        if (!$category || $category['status'] != 1) {
            $this->redirect('/');
            return;
        }
        
        // 获取分类路径
        $categoryPath = $this->categoryModel->getCategoryPath($categoryId);
        
        // 获取子分类ID
        $childrenIds = $this->categoryModel->getChildrenIds($categoryId);
        
        // 获取链接
        $links = [];
        foreach ($childrenIds as $id) {
            $categoryLinks = $this->linkModel->getActiveLinks($id);
            $links = array_merge($links, $categoryLinks);
        }
        
        $data = [
            'title' => $category['name'] . ' - ' . SITE_NAME,
            'category' => $category,
            'categoryPath' => $categoryPath,
            'links' => $links
        ];
        
        $this->render('home/category', $data);
    }
    
    /**
     * API接口 - 获取分类
     */
    public function apiCategories()
    {
        $categories = $this->categoryModel->getActiveCategories();
        $this->json(['success' => true, 'data' => $categories]);
    }
    
    /**
     * API接口 - 获取链接
     */
    public function apiLinks()
    {
        $categoryId = $this->getGet('category_id');
        $links = $this->linkModel->getActiveLinks($categoryId);
        $this->json(['success' => true, 'data' => $links]);
    }

    /**
     * 获取当前主题
     */
    private function getCurrentTheme()
    {
        // 检查预览主题
        if (isset($_SESSION['preview_theme'])) {
            return $_SESSION['preview_theme'];
        }

        // 从设置中获取
        $setting = $this->db->fetch('SELECT setting_value FROM settings WHERE setting_key = ?', ['active_theme']);
        return $setting ? $setting['setting_value'] : 'default';
    }

    /**
     * 获取热点新闻（模拟数据）
     */
    private function getHotNews()
    {
        return [
            ['title' => '科技创新推动数字化转型', 'url' => '#'],
            ['title' => '人工智能在医疗领域的应用', 'url' => '#'],
            ['title' => '绿色能源发展新趋势', 'url' => '#'],
            ['title' => '5G技术助力智慧城市建设', 'url' => '#'],
            ['title' => '区块链技术在金融领域的突破', 'url' => '#'],
            ['title' => '量子计算研究取得重大进展', 'url' => '#'],
            ['title' => '新能源汽车市场快速增长', 'url' => '#'],
            ['title' => '云计算服务持续优化升级', 'url' => '#'],
            ['title' => '物联网设备安全防护加强', 'url' => '#'],
            ['title' => '虚拟现实技术应用场景扩展', 'url' => '#']
        ];
    }

    /**
     * 获取百度热搜（模拟数据）
     */
    private function getBaiduHot()
    {
        return [
            ['title' => '今日热点话题讨论', 'url' => '#'],
            ['title' => '科技新闻热门事件', 'url' => '#'],
            ['title' => '社会热点关注焦点', 'url' => '#'],
            ['title' => '娱乐圈最新动态', 'url' => '#'],
            ['title' => '体育赛事精彩回顾', 'url' => '#'],
            ['title' => '财经市场分析报告', 'url' => '#'],
            ['title' => '教育政策最新变化', 'url' => '#'],
            ['title' => '健康生活方式推荐', 'url' => '#'],
            ['title' => '旅游景点热门推荐', 'url' => '#'],
            ['title' => '美食文化深度探索', 'url' => '#']
        ];
    }
}
