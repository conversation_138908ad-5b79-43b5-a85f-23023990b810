# NavStack .htaccess 配置文件

# 启用重写引擎
RewriteEngine On

# 安全设置 - 禁止访问敏感文件
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

# 安装完成后请取消注释以下代码以保护安装文件
# <Files "install.php">
#     Order Allow,Deny
#     Deny from all
# </Files>

# 禁止访问 .htaccess 文件
<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

# 禁止访问数据目录
<Directory "data">
    Order Allow,Deny
    Deny from all
</Directory>

# 禁止访问应用目录
<Directory "app">
    Order Allow,Deny
    Deny from all
</Directory>

# 禁止访问模板目录
<Directory "templates">
    Order Allow,Deny
    Deny from all
</Directory>

# 禁止显示目录列表
Options -Indexes

# 设置默认字符集
AddDefaultCharset UTF-8

# 启用压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# 设置缓存
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/plain "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
</IfModule>

# 安全头设置
<IfModule mod_headers.c>
    # 防止点击劫持
    Header always append X-Frame-Options SAMEORIGIN
    
    # 防止MIME类型嗅探
    Header always set X-Content-Type-Options nosniff
    
    # XSS保护
    Header always set X-XSS-Protection "1; mode=block"
    
    # 引用策略
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # 内容安全策略
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self';"
</IfModule>

# URL重写规则（可选，用于美化URL）
# 如果需要美化URL，可以取消注释以下规则

# 重写首页
# RewriteRule ^$ index.php [L]

# 重写分类页面
# RewriteRule ^category/([0-9]+)/?$ index.php?c=home&action=category&id=$1 [L,QSA]

# 重写搜索页面
# RewriteRule ^search/?$ index.php?c=home&action=search [L,QSA]

# 重写管理后台
# RewriteRule ^admin/?$ index.php?c=admin [L,QSA]
# RewriteRule ^admin/([a-zA-Z]+)/?$ index.php?c=admin&action=$1 [L,QSA]

# 错误页面
ErrorDocument 404 /index.php?c=error&action=404
ErrorDocument 403 /index.php?c=error&action=403
ErrorDocument 500 /index.php?c=error&action=500

# 防止热链接（可选）
# RewriteCond %{HTTP_REFERER} !^$
# RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?yourdomain.com [NC]
# RewriteRule \.(jpg|jpeg|png|gif|ico)$ - [NC,F,L]

# PHP设置
<IfModule mod_php7.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    php_value max_execution_time 300
    php_value max_input_vars 3000
    php_value memory_limit 256M
</IfModule>

# 隐藏PHP版本信息
<IfModule mod_headers.c>
    Header unset X-Powered-By
</IfModule>
