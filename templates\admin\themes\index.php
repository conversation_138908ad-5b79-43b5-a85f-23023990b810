<?php
$content = ob_start();
$pageTitle = '主题管理';
?>

<!-- 操作按钮 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <button type="button" class="btn btn-primary" onclick="installAllThemes()">
            <i class="fas fa-download"></i>
            安装所有预设主题
        </button>
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#uploadThemeModal">
            <i class="fas fa-upload"></i>
            上传主题
        </button>
    </div>
    <div>
        <span class="text-muted">当前激活：</span>
        <span class="badge bg-primary"><?= $activeTheme['title'] ?? '默认主题' ?></span>
    </div>
</div>

<!-- 主题网格 -->
<div class="row">
    <?php foreach ($themes as $theme): ?>
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card theme-card <?= $theme['is_active'] ? 'border-primary' : '' ?>">
            <?php if ($theme['is_active']): ?>
            <div class="card-header bg-primary text-white text-center">
                <i class="fas fa-check-circle"></i>
                当前使用
            </div>
            <?php endif; ?>
            
            <div class="card-body text-center">
                <!-- 主题预览图 -->
                <div class="theme-preview mb-3">
                    <?php if (!empty($theme['preview_image'])): ?>
                    <img src="<?= $this->escape($theme['preview_image']) ?>" 
                         alt="<?= $this->escape($theme['title']) ?>" 
                         class="img-fluid rounded">
                    <?php else: ?>
                    <div class="preview-placeholder">
                        <i class="fas fa-image fa-3x text-muted"></i>
                        <p class="text-muted mt-2">暂无预览图</p>
                    </div>
                    <?php endif; ?>
                </div>
                
                <!-- 主题信息 -->
                <h5 class="card-title"><?= $this->escape($theme['title']) ?></h5>
                <p class="card-text text-muted"><?= $this->escape($theme['description']) ?></p>
                
                <div class="theme-meta">
                    <small class="text-muted">
                        <i class="fas fa-user"></i> <?= $this->escape($theme['author']) ?>
                        <span class="mx-2">|</span>
                        <i class="fas fa-tag"></i> v<?= $this->escape($theme['version']) ?>
                    </small>
                </div>
            </div>
            
            <div class="card-footer">
                <div class="btn-group w-100" role="group">
                    <?php if (!$theme['is_active']): ?>
                    <button type="button" class="btn btn-primary btn-sm" 
                            onclick="activateTheme(<?= $theme['id'] ?>)">
                        <i class="fas fa-check"></i>
                        启用
                    </button>
                    <?php endif; ?>
                    
                    <button type="button" class="btn btn-outline-secondary btn-sm" 
                            onclick="previewTheme('<?= $theme['name'] ?>')">
                        <i class="fas fa-eye"></i>
                        预览
                    </button>
                    
                    <button type="button" class="btn btn-outline-info btn-sm" 
                            onclick="configTheme(<?= $theme['id'] ?>)">
                        <i class="fas fa-cog"></i>
                        配置
                    </button>
                    
                    <?php if (!$theme['is_active'] && $theme['name'] !== 'default'): ?>
                    <button type="button" class="btn btn-outline-danger btn-sm" 
                            onclick="uninstallTheme(<?= $theme['id'] ?>)">
                        <i class="fas fa-trash"></i>
                        删除
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endforeach; ?>
</div>

<!-- 预设主题 -->
<?php if (!empty($presetThemes)): ?>
<div class="mt-5">
    <h4>
        <i class="fas fa-store"></i>
        主题商店
    </h4>
    <p class="text-muted">以下是可安装的预设主题</p>
    
    <div class="row">
        <?php foreach ($presetThemes as $preset): ?>
        <?php 
        $isInstalled = false;
        foreach ($themes as $theme) {
            if ($theme['name'] === $preset['name']) {
                $isInstalled = true;
                break;
            }
        }
        ?>
        
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card preset-theme-card">
                <div class="card-body text-center">
                    <!-- 预设主题预览图 -->
                    <div class="theme-preview mb-3">
                        <?php if (!empty($preset['preview_image'])): ?>
                        <img src="<?= $this->escape($preset['preview_image']) ?>" 
                             alt="<?= $this->escape($preset['title']) ?>" 
                             class="img-fluid rounded">
                        <?php else: ?>
                        <div class="preview-placeholder">
                            <i class="fas fa-image fa-3x text-muted"></i>
                            <p class="text-muted mt-2">暂无预览图</p>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- 主题信息 -->
                    <h5 class="card-title"><?= $this->escape($preset['title']) ?></h5>
                    <p class="card-text text-muted"><?= $this->escape($preset['description']) ?></p>
                    
                    <div class="theme-meta">
                        <small class="text-muted">
                            <i class="fas fa-user"></i> <?= $this->escape($preset['author']) ?>
                            <span class="mx-2">|</span>
                            <i class="fas fa-tag"></i> v<?= $this->escape($preset['version']) ?>
                        </small>
                    </div>
                </div>
                
                <div class="card-footer text-center">
                    <?php if ($isInstalled): ?>
                    <span class="badge bg-success">
                        <i class="fas fa-check"></i>
                        已安装
                    </span>
                    <?php else: ?>
                    <button type="button" class="btn btn-primary btn-sm" 
                            onclick="installPresetTheme('<?= $preset['name'] ?>')">
                        <i class="fas fa-download"></i>
                        安装主题
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
</div>
<?php endif; ?>

<!-- 上传主题模态框 -->
<div class="modal fade" id="uploadThemeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">上传主题</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadThemeForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="themeFile" class="form-label">主题文件 (ZIP格式)</label>
                        <input type="file" class="form-control" id="themeFile" name="theme_file" accept=".zip" required>
                        <div class="form-text">请上传ZIP格式的主题包</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="uploadTheme()">上传</button>
            </div>
        </div>
    </div>
</div>

<style>
.theme-card {
    transition: transform 0.2s;
}
.theme-card:hover {
    transform: translateY(-5px);
}
.theme-preview {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 0.375rem;
}
.preview-placeholder {
    text-align: center;
}
.theme-meta {
    margin-top: 10px;
}
.preset-theme-card {
    border: 2px dashed #dee2e6;
}
.preset-theme-card:hover {
    border-color: #007bff;
}
</style>

<script>
// 激活主题
function activateTheme(themeId) {
    if (confirm('确定要激活这个主题吗？')) {
        fetch('<?= $this->url("theme", "activate") ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'theme_id=' + themeId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || '激活失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('操作失败');
        });
    }
}

// 预览主题
function previewTheme(themeName) {
    window.open('<?= $this->url("theme", "preview") ?>/' + themeName, '_blank');
}

// 配置主题
function configTheme(themeId) {
    window.location.href = '<?= $this->url("theme", "config") ?>/' + themeId;
}

// 卸载主题
function uninstallTheme(themeId) {
    if (confirm('确定要删除这个主题吗？此操作不可恢复！')) {
        fetch('<?= $this->url("theme", "uninstall") ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'theme_id=' + themeId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || '删除失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('操作失败');
        });
    }
}

// 安装预设主题
function installPresetTheme(themeName) {
    fetch('<?= $this->url("theme", "installPreset") ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'theme_name=' + themeName
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || '安装失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('操作失败');
    });
}

// 安装所有预设主题
function installAllThemes() {
    if (confirm('确定要安装所有预设主题吗？')) {
        fetch('<?= $this->url("theme", "installAll") ?>', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);
            if (data.success) {
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('操作失败');
        });
    }
}

// 上传主题
function uploadTheme() {
    const formData = new FormData(document.getElementById('uploadThemeForm'));
    
    fetch('<?= $this->url("theme", "upload") ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || '上传失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('上传失败');
    });
}
</script>

<?php
$content = ob_get_clean();
include TEMPLATE_PATH . '/admin/layout.php';
?>
