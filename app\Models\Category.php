<?php

namespace Models;

use Core\Model;

/**
 * 分类模型
 */
class Category extends Model
{
    protected $table = 'categories';
    
    /**
     * 获取所有启用的分类
     */
    public function getActiveCategories()
    {
        return $this->findAll('status = 1', [], 'sort_order ASC, id ASC');
    }
    
    /**
     * 获取分类树
     */
    public function getCategoryTree($parentId = 0)
    {
        $categories = $this->findAll('parent_id = :parent_id AND status = 1', 
                                   ['parent_id' => $parentId], 
                                   'sort_order ASC, id ASC');
        
        foreach ($categories as &$category) {
            $category['children'] = $this->getCategoryTree($category['id']);
        }
        
        return $categories;
    }
    
    /**
     * 获取分类及其链接数量
     */
    public function getCategoriesWithLinkCount()
    {
        $sql = "SELECT c.*, COUNT(l.id) as link_count 
                FROM categories c 
                LEFT JOIN links l ON c.id = l.category_id AND l.status = 1
                WHERE c.status = 1 
                GROUP BY c.id 
                ORDER BY c.sort_order ASC, c.id ASC";
        
        return $this->db->fetchAll($sql);
    }
    
    /**
     * 获取父级分类路径
     */
    public function getCategoryPath($categoryId)
    {
        $path = [];
        $category = $this->findById($categoryId);
        
        while ($category) {
            array_unshift($path, $category);
            if ($category['parent_id'] == 0) {
                break;
            }
            $category = $this->findById($category['parent_id']);
        }
        
        return $path;
    }
    
    /**
     * 检查分类名称是否存在
     */
    public function nameExists($name, $excludeId = null)
    {
        $where = 'name = :name';
        $params = ['name' => $name];
        
        if ($excludeId) {
            $where .= ' AND id != :id';
            $params['id'] = $excludeId;
        }
        
        return $this->findOne($where, $params) !== false;
    }
    
    /**
     * 获取子分类ID列表
     */
    public function getChildrenIds($parentId)
    {
        $ids = [$parentId];
        $children = $this->findAll('parent_id = :parent_id', ['parent_id' => $parentId]);
        
        foreach ($children as $child) {
            $ids = array_merge($ids, $this->getChildrenIds($child['id']));
        }
        
        return $ids;
    }
    
    /**
     * 更新排序
     */
    public function updateSort($id, $sortOrder)
    {
        return $this->update($id, ['sort_order' => $sortOrder]);
    }
    
    /**
     * 删除分类（软删除）
     */
    public function softDelete($id)
    {
        return $this->update($id, ['status' => 0]);
    }
    
    /**
     * 恢复分类
     */
    public function restore($id)
    {
        return $this->update($id, ['status' => 1]);
    }
}
