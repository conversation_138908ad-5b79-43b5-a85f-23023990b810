<?php

namespace Core;

/**
 * 模型基类
 */
class Model
{
    protected $db;
    protected $table;
    
    public function __construct()
    {
        $this->db = Database::getInstance();
    }
    
    /**
     * 查找所有记录
     */
    public function findAll($where = '', $params = [], $orderBy = '', $limit = '')
    {
        $sql = "SELECT * FROM {$this->table}";
        
        if (!empty($where)) {
            $sql .= " WHERE {$where}";
        }
        
        if (!empty($orderBy)) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        if (!empty($limit)) {
            $sql .= " LIMIT {$limit}";
        }
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * 根据ID查找记录
     */
    public function findById($id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE id = :id";
        return $this->db->fetch($sql, ['id' => $id]);
    }
    
    /**
     * 查找单条记录
     */
    public function findOne($where = '', $params = [])
    {
        $sql = "SELECT * FROM {$this->table}";
        
        if (!empty($where)) {
            $sql .= " WHERE {$where}";
        }
        
        $sql .= " LIMIT 1";
        
        return $this->db->fetch($sql, $params);
    }
    
    /**
     * 创建记录
     */
    public function create($data)
    {
        // 添加时间戳
        if (!isset($data['created_at'])) {
            $data['created_at'] = date('Y-m-d H:i:s');
        }
        
        return $this->db->insert($this->table, $data);
    }
    
    /**
     * 更新记录
     */
    public function update($id, $data)
    {
        // 添加更新时间戳
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->update($this->table, $data, 'id = :id', ['id' => $id]);
    }
    
    /**
     * 删除记录
     */
    public function delete($id)
    {
        return $this->db->delete($this->table, 'id = :id', ['id' => $id]);
    }
    
    /**
     * 统计记录数
     */
    public function count($where = '', $params = [])
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";
        
        if (!empty($where)) {
            $sql .= " WHERE {$where}";
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] ?? 0;
    }
    
    /**
     * 分页查询
     */
    public function paginate($page = 1, $perPage = 20, $where = '', $params = [], $orderBy = '')
    {
        $offset = ($page - 1) * $perPage;
        
        // 获取总数
        $total = $this->count($where, $params);
        
        // 获取数据
        $sql = "SELECT * FROM {$this->table}";
        
        if (!empty($where)) {
            $sql .= " WHERE {$where}";
        }
        
        if (!empty($orderBy)) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        $sql .= " LIMIT {$perPage} OFFSET {$offset}";
        
        $data = $this->db->fetchAll($sql, $params);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
}
