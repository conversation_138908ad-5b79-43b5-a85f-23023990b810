<?php
$content = ob_start();
$pageTitle = '控制台';
?>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            总链接数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= $stats['total_links'] ?? 0 ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-link fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            活跃链接
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= $stats['active_links'] ?? 0 ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            总分类数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= $stats['total_categories'] ?? 0 ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-folder fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            总点击数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?= number_format($stats['total_clicks'] ?? 0) ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-mouse-pointer fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快捷操作 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-rocket"></i>
                    快捷操作
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="<?= $this->url('link', 'add') ?>" class="btn btn-primary btn-block">
                            <i class="fas fa-plus"></i>
                            添加链接
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= $this->url('category', 'add') ?>" class="btn btn-success btn-block">
                            <i class="fas fa-folder-plus"></i>
                            添加分类
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= $this->url('theme') ?>" class="btn btn-info btn-block">
                            <i class="fas fa-palette"></i>
                            主题管理
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= $this->url('admin', 'settings') ?>" class="btn btn-warning btn-block">
                            <i class="fas fa-cog"></i>
                            系统设置
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最新链接和热门链接 -->
<div class="row">
    <!-- 最新链接 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-clock"></i>
                    最新链接
                </h6>
            </div>
            <div class="card-body">
                <?php if (!empty($latestLinks)): ?>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>标题</th>
                                <th>点击数</th>
                                <th>添加时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($latestLinks as $link): ?>
                            <tr>
                                <td>
                                    <a href="<?= $this->escape($link['url']) ?>" target="_blank" class="text-decoration-none">
                                        <?= $this->escape($this->truncate($link['title'], 30)) ?>
                                    </a>
                                </td>
                                <td><?= $link['click_count'] ?></td>
                                <td><?= $this->formatDate($link['created_at'], 'm-d H:i') ?></td>
                                <td>
                                    <a href="<?= $this->url('link', 'edit', ['id' => $link['id']]) ?>" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <p class="text-muted text-center">暂无链接</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- 热门链接 -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-fire"></i>
                    热门链接
                </h6>
            </div>
            <div class="card-body">
                <?php if (!empty($popularLinks)): ?>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>标题</th>
                                <th>点击数</th>
                                <th>添加时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($popularLinks as $link): ?>
                            <tr>
                                <td>
                                    <a href="<?= $this->escape($link['url']) ?>" target="_blank" class="text-decoration-none">
                                        <?= $this->escape($this->truncate($link['title'], 30)) ?>
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-danger"><?= $link['click_count'] ?></span>
                                </td>
                                <td><?= $this->formatDate($link['created_at'], 'm-d H:i') ?></td>
                                <td>
                                    <a href="<?= $this->url('link', 'edit', ['id' => $link['id']]) ?>" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <p class="text-muted text-center">暂无链接</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- 系统信息 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle"></i>
                    系统信息
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td><strong>系统版本：</strong></td>
                                <td>TwoNav v2.0.0</td>
                            </tr>
                            <tr>
                                <td><strong>PHP版本：</strong></td>
                                <td><?= PHP_VERSION ?></td>
                            </tr>
                            <tr>
                                <td><strong>数据库类型：</strong></td>
                                <td><?= DB_TYPE ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td><strong>服务器时间：</strong></td>
                                <td><?= date('Y-m-d H:i:s') ?></td>
                            </tr>
                            <tr>
                                <td><strong>运行时间：</strong></td>
                                <td><?= function_exists('sys_getloadavg') ? implode(', ', sys_getloadavg()) : '未知' ?></td>
                            </tr>
                            <tr>
                                <td><strong>内存使用：</strong></td>
                                <td><?= round(memory_get_usage() / 1024 / 1024, 2) ?> MB</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-xs {
    font-size: 0.7rem;
}
.btn-block {
    width: 100%;
}
</style>

<?php
$content = ob_get_clean();
include TEMPLATE_PATH . '/admin/layout.php';
?>
