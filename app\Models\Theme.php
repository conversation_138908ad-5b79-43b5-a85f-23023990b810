<?php

namespace Models;

use Core\Model;

/**
 * 主题模型
 */
class Theme extends Model
{
    protected $table = 'themes';
    
    /**
     * 获取当前激活的主题
     */
    public function getActiveTheme()
    {
        return $this->findOne('is_active = 1');
    }
    
    /**
     * 获取所有可用主题
     */
    public function getAllThemes()
    {
        return $this->findAll('1=1', [], 'name ASC');
    }
    
    /**
     * 根据名称获取主题
     */
    public function getThemeByName($name)
    {
        return $this->findOne('name = :name', ['name' => $name]);
    }
    
    /**
     * 激活主题
     */
    public function activateTheme($themeId)
    {
        // 先取消所有主题的激活状态
        $this->db->query("UPDATE {$this->table} SET is_active = 0");
        
        // 激活指定主题
        return $this->update($themeId, ['is_active' => 1]);
    }
    
    /**
     * 安装主题
     */
    public function installTheme($themeData)
    {
        // 检查主题是否已存在
        $existing = $this->getThemeByName($themeData['name']);
        if ($existing) {
            return false;
        }
        
        return $this->create($themeData);
    }
    
    /**
     * 卸载主题
     */
    public function uninstallTheme($themeId)
    {
        $theme = $this->findById($themeId);
        if (!$theme) {
            return false;
        }
        
        // 不能卸载当前激活的主题
        if ($theme['is_active']) {
            return false;
        }
        
        return $this->delete($themeId);
    }
    
    /**
     * 更新主题配置
     */
    public function updateThemeConfig($themeId, $config)
    {
        $configJson = is_array($config) ? json_encode($config) : $config;
        return $this->update($themeId, ['config' => $configJson]);
    }
    
    /**
     * 获取主题配置
     */
    public function getThemeConfig($themeId)
    {
        $theme = $this->findById($themeId);
        if (!$theme || empty($theme['config'])) {
            return [];
        }
        
        return json_decode($theme['config'], true) ?: [];
    }
    
    /**
     * 获取主题预设列表
     */
    public function getThemePresets()
    {
        return [
            'webstack' => [
                'name' => 'webstack',
                'title' => 'WebStack主题',
                'description' => '仿WebStack官网风格，简洁美观的导航主题',
                'version' => '1.0.0',
                'author' => 'NavStack',
                'preview_image' => '/static/images/themes/webstack.jpg'
            ],
            'baisui' => [
                'name' => 'baisui',
                'title' => '百素主题',
                'description' => '百素风格主题，清新简约',
                'version' => '1.0.0',
                'author' => 'NavStack',
                'preview_image' => '/static/images/themes/baisui.jpg'
            ],
            'qixia' => [
                'name' => 'qixia',
                'title' => '七夏主题',
                'description' => '七夏风格主题，温馨舒适',
                'version' => '1.0.0',
                'author' => 'NavStack',
                'preview_image' => '/static/images/themes/qixia.jpg'
            ],
            'suozhang' => [
                'name' => 'suozhang',
                'title' => '所长导航',
                'description' => '所长导航风格主题',
                'version' => '1.0.0',
                'author' => 'NavStack',
                'preview_image' => '/static/images/themes/suozhang.jpg'
            ],
            'simple' => [
                'name' => 'simple',
                'title' => '简约主题',
                'description' => '极简风格主题，专注内容',
                'version' => '1.0.0',
                'author' => 'NavStack',
                'preview_image' => '/static/images/themes/simple.jpg'
            ]
        ];
    }
    
    /**
     * 批量安装预设主题
     */
    public function installPresetThemes()
    {
        $presets = $this->getThemePresets();
        $installed = 0;
        
        foreach ($presets as $preset) {
            if (!$this->getThemeByName($preset['name'])) {
                if ($this->create($preset)) {
                    $installed++;
                }
            }
        }
        
        return $installed;
    }
    
    /**
     * 检查主题文件是否存在
     */
    public function themeFileExists($themeName)
    {
        $themePath = TEMPLATE_PATH . '/themes/' . $themeName;
        return is_dir($themePath) && file_exists($themePath . '/index.php');
    }
    
    /**
     * 获取主题模板路径
     */
    public function getThemeTemplatePath($themeName)
    {
        return TEMPLATE_PATH . '/themes/' . $themeName;
    }
}
