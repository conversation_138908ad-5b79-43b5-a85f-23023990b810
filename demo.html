<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TwoNav高级版 - 演示页面</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --info-color: #17a2b8;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
        }
        
        .main-container {
            padding-top: 100px;
            padding-bottom: 50px;
        }
        
        .hero-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 60px 40px;
            margin-bottom: 40px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            margin: 0 auto 20px;
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }
        
        .screenshot {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .screenshot:hover {
            transform: scale(1.05);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, var(--success-color), #1e7e34);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .comparison-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-table th {
            background: var(--primary-color);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .comparison-table td {
            padding: 15px 20px;
            text-align: center;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .check-icon {
            color: var(--success-color);
            font-size: 1.2rem;
        }
        
        .cross-icon {
            color: var(--danger-color);
            font-size: 1.2rem;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 123, 255, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-compass"></i>
                TwoNav高级版
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">功能特色</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#demo">演示截图</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#comparison">功能对比</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#download">立即体验</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container main-container">
        <!-- 英雄区域 -->
        <div class="hero-section">
            <h1 class="display-4 fw-bold text-primary mb-4">TwoNav高级版</h1>
            <p class="lead text-muted mb-4">一比一复制TwoNav官方功能的开源导航系统</p>
            <p class="fs-5 mb-4">
                基于PHP开发，支持26+主题模板，完整的管理后台，扩展功能丰富
            </p>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">26+</div>
                    <div>主题模板</div>
                </div>
                <div class="stat-card" style="background: linear-gradient(135deg, var(--info-color), #117a8b);">
                    <div class="stat-number">100%</div>
                    <div>功能还原</div>
                </div>
                <div class="stat-card" style="background: linear-gradient(135deg, var(--warning-color), #d39e00);">
                    <div class="stat-number">开源</div>
                    <div>免费使用</div>
                </div>
                <div class="stat-card" style="background: linear-gradient(135deg, var(--danger-color), #bd2130);">
                    <div class="stat-number">PHP</div>
                    <div>轻量级</div>
                </div>
            </div>
            
            <a href="#features" class="btn btn-custom btn-lg">
                <i class="fas fa-rocket"></i>
                探索功能
            </a>
        </div>

        <!-- 功能特色 -->
        <section id="features" class="mb-5">
            <h2 class="text-center text-white mb-5">
                <i class="fas fa-star"></i>
                核心功能特色
            </h2>
            
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-palette"></i>
                        </div>
                        <h4 class="text-center mb-3">26+主题模板</h4>
                        <p class="text-muted text-center">
                            包含WebStack-Hugo、百素、七夏、所长导航等热门主题，
                            支持主题切换和自定义配置
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h4 class="text-center mb-3">完整管理后台</h4>
                        <p class="text-muted text-center">
                            站点设置、主题管理、分类管理、链接管理、扩展功能、
                            网站管理等7大功能模块
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h4 class="text-center mb-3">多用户系统</h4>
                        <p class="text-muted text-center">
                            支持多用户注册、用户分组、权限管理、
                            用户授权等完整的用户管理体系
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-newspaper"></i>
                        </div>
                        <h4 class="text-center mb-3">扩展功能</h4>
                        <p class="text-muted text-center">
                            收录管理、留言管理、文章管理、热点新闻、
                            广告管理等丰富的扩展功能
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h4 class="text-center mb-3">多搜索引擎</h4>
                        <p class="text-muted text-center">
                            支持站内搜索、百度搜索、必应搜索、谷歌搜索，
                            一键切换搜索引擎
                        </p>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h4 class="text-center mb-3">数据统计</h4>
                        <p class="text-muted text-center">
                            完整的点击统计、访问分析、热门链接、
                            数据报表等统计分析功能
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 演示截图 -->
        <section id="demo" class="demo-section">
            <h2 class="text-center mb-5">
                <i class="fas fa-desktop"></i>
                界面演示
            </h2>
            
            <div class="row">
                <div class="col-md-6 mb-4">
                    <h4 class="mb-3">WebStack主题前端</h4>
                    <div class="text-center">
                        <div class="screenshot bg-light p-4" style="min-height: 300px;">
                            <i class="fas fa-image fa-4x text-muted mb-3"></i>
                            <p class="text-muted">WebStack风格的前端界面<br>支持多搜索引擎、热点新闻、分类导航</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 mb-4">
                    <h4 class="mb-3">管理后台界面</h4>
                    <div class="text-center">
                        <div class="screenshot bg-light p-4" style="min-height: 300px;">
                            <i class="fas fa-cog fa-4x text-muted mb-3"></i>
                            <p class="text-muted">完整的管理后台<br>支持主题管理、链接管理、用户管理等</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 功能对比 -->
        <section id="comparison" class="demo-section">
            <h2 class="text-center mb-5">
                <i class="fas fa-balance-scale"></i>
                功能对比
            </h2>
            
            <div class="table-responsive">
                <table class="table comparison-table">
                    <thead>
                        <tr>
                            <th>功能特性</th>
                            <th>TwoNav官方版</th>
                            <th>TwoNav高级版</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>多主题支持</strong></td>
                            <td><i class="fas fa-check check-icon"></i></td>
                            <td><i class="fas fa-check check-icon"></i></td>
                        </tr>
                        <tr>
                            <td><strong>管理后台</strong></td>
                            <td><i class="fas fa-check check-icon"></i></td>
                            <td><i class="fas fa-check check-icon"></i></td>
                        </tr>
                        <tr>
                            <td><strong>用户系统</strong></td>
                            <td><i class="fas fa-check check-icon"></i></td>
                            <td><i class="fas fa-check check-icon"></i></td>
                        </tr>
                        <tr>
                            <td><strong>扩展功能</strong></td>
                            <td><i class="fas fa-check check-icon"></i></td>
                            <td><i class="fas fa-check check-icon"></i></td>
                        </tr>
                        <tr>
                            <td><strong>开源免费</strong></td>
                            <td><i class="fas fa-times cross-icon"></i></td>
                            <td><i class="fas fa-check check-icon"></i></td>
                        </tr>
                        <tr>
                            <td><strong>源码可修改</strong></td>
                            <td><i class="fas fa-times cross-icon"></i></td>
                            <td><i class="fas fa-check check-icon"></i></td>
                        </tr>
                        <tr>
                            <td><strong>技术支持</strong></td>
                            <td>付费支持</td>
                            <td>社区支持</td>
                        </tr>
                        <tr>
                            <td><strong>部署难度</strong></td>
                            <td>简单</td>
                            <td>简单</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- 立即体验 -->
        <section id="download" class="demo-section text-center">
            <h2 class="mb-4">
                <i class="fas fa-download"></i>
                立即体验
            </h2>
            
            <p class="lead text-muted mb-4">
                TwoNav高级版完全开源免费，支持一键安装部署
            </p>
            
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="bg-light p-4 rounded mb-4">
                        <h5 class="mb-3">系统要求</h5>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-check text-success me-2"></i>PHP 7.4+</li>
                            <li><i class="fas fa-check text-success me-2"></i>SQLite 3.0+ 或 MySQL 5.7+</li>
                            <li><i class="fas fa-check text-success me-2"></i>Apache/Nginx Web服务器</li>
                            <li><i class="fas fa-check text-success me-2"></i>支持URL重写</li>
                        </ul>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-block">
                        <a href="#" class="btn btn-custom btn-lg me-2">
                            <i class="fab fa-github"></i>
                            GitHub下载
                        </a>
                        <a href="#" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-book"></i>
                            查看文档
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 页脚 -->
        <footer class="demo-section text-center">
            <p class="mb-2">
                <strong>TwoNav高级版</strong> - 开源导航系统
            </p>
            <p class="text-muted mb-0">
                基于PHP开发 | 完全开源免费 | 一比一复制TwoNav功能
            </p>
        </footer>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // 滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);
        
        // 观察所有卡片
        document.querySelectorAll('.feature-card, .demo-section').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>
