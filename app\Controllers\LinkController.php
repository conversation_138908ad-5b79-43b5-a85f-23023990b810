<?php

namespace Controllers;

use Core\Controller;
use Models\Link;

/**
 * 链接控制器
 */
class LinkController extends Controller
{
    private $linkModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->linkModel = new Link();
    }
    
    /**
     * 点击链接
     */
    public function click($id = null)
    {
        if (!$id) {
            $this->json(['success' => false, 'message' => '链接ID不能为空']);
            return;
        }
        
        // 获取链接信息
        $link = $this->linkModel->findById($id);
        if (!$link || $link['status'] != 1) {
            $this->json(['success' => false, 'message' => '链接不存在或已禁用']);
            return;
        }
        
        // 记录点击统计
        $this->recordClick($id);
        
        // 增加点击次数
        $this->linkModel->incrementClick($id);
        
        // 返回链接URL
        $this->json(['success' => true, 'url' => $link['url']]);
    }
    
    /**
     * 直接跳转
     */
    public function go($id = null)
    {
        if (!$id) {
            $this->redirect('/');
            return;
        }
        
        // 获取链接信息
        $link = $this->linkModel->findById($id);
        if (!$link || $link['status'] != 1) {
            $this->redirect('/');
            return;
        }
        
        // 记录点击统计
        $this->recordClick($id);
        
        // 增加点击次数
        $this->linkModel->incrementClick($id);
        
        // 跳转到目标URL
        $this->redirect($link['url']);
    }
    
    /**
     * 管理页面
     */
    public function index()
    {
        $this->requireAdmin();
        
        $page = max(1, intval($this->getGet('page', 1)));
        $categoryId = $this->getGet('category_id');
        $keyword = $this->getGet('keyword', '');
        
        // 构建查询条件
        $where = '1=1';
        $params = [];
        
        if ($categoryId) {
            $where .= ' AND category_id = :category_id';
            $params['category_id'] = $categoryId;
        }
        
        if (!empty($keyword)) {
            $where .= ' AND (title LIKE :keyword OR url LIKE :keyword OR description LIKE :keyword)';
            $params['keyword'] = "%{$keyword}%";
        }
        
        // 分页查询
        $result = $this->linkModel->paginate($page, PAGE_SIZE, $where, $params, 'created_at DESC');
        
        // 获取分类列表
        $categoryModel = new \Models\Category();
        $categories = $categoryModel->getActiveCategories();
        
        $data = [
            'title' => '链接管理 - ' . SITE_NAME,
            'links' => $result['data'],
            'pagination' => $result,
            'categories' => $categories,
            'currentCategoryId' => $categoryId,
            'keyword' => $keyword
        ];
        
        $this->render('admin/links/index', $data);
    }
    
    /**
     * 添加链接
     */
    public function add()
    {
        $this->requireAdmin();
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $data = [
                'title' => $this->sanitize($this->getPost('title')),
                'url' => $this->sanitize($this->getPost('url')),
                'description' => $this->sanitize($this->getPost('description')),
                'icon' => $this->sanitize($this->getPost('icon')),
                'category_id' => intval($this->getPost('category_id')),
                'sort_order' => intval($this->getPost('sort_order', 0)),
                'status' => intval($this->getPost('status', 1))
            ];
            
            // 验证数据
            if (empty($data['title']) || empty($data['url'])) {
                $this->json(['success' => false, 'message' => '标题和URL不能为空']);
                return;
            }
            
            // 检查URL是否已存在
            if ($this->linkModel->urlExists($data['url'])) {
                $this->json(['success' => false, 'message' => 'URL已存在']);
                return;
            }
            
            // 创建链接
            $id = $this->linkModel->create($data);
            if ($id) {
                $this->json(['success' => true, 'message' => '添加成功', 'id' => $id]);
            } else {
                $this->json(['success' => false, 'message' => '添加失败']);
            }
            return;
        }
        
        // 获取分类列表
        $categoryModel = new \Models\Category();
        $categories = $categoryModel->getActiveCategories();
        
        $data = [
            'title' => '添加链接 - ' . SITE_NAME,
            'categories' => $categories
        ];
        
        $this->render('admin/links/add', $data);
    }
    
    /**
     * 记录点击统计
     */
    private function recordClick($linkId)
    {
        $data = [
            'link_id' => $linkId,
            'ip_address' => $this->getClientIp(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'referer' => $_SERVER['HTTP_REFERER'] ?? '',
            'click_time' => date('Y-m-d H:i:s')
        ];
        
        $this->db->insert('click_stats', $data);
    }
}
