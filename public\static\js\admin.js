/**
 * 管理后台JavaScript功能
 */

// 全局配置
const AdminConfig = {
    baseUrl: window.location.origin,
    apiUrl: window.location.origin + '/index.php?c=api',
    csrfToken: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
    timeout: 30000
};

// 工具函数
const Utils = {
    /**
     * 发送AJAX请求
     */
    ajax: function(url, options = {}) {
        const defaults = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            timeout: AdminConfig.timeout
        };
        
        const config = Object.assign(defaults, options);
        
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            
            xhr.open(config.method, url, true);
            
            // 设置请求头
            Object.keys(config.headers).forEach(key => {
                xhr.setRequestHeader(key, config.headers[key]);
            });
            
            // 设置超时
            xhr.timeout = config.timeout;
            
            xhr.onload = function() {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (e) {
                        resolve(xhr.responseText);
                    }
                } else {
                    reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                }
            };
            
            xhr.onerror = function() {
                reject(new Error('网络错误'));
            };
            
            xhr.ontimeout = function() {
                reject(new Error('请求超时'));
            };
            
            xhr.send(config.body);
        });
    },
    
    /**
     * 显示消息提示
     */
    showMessage: function(message, type = 'info', duration = 3000) {
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';
        
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="fas fa-${this.getIcon(type)}"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // 创建消息容器
        let container = document.getElementById('message-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'message-container';
            container.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
            document.body.appendChild(container);
        }
        
        const alertElement = document.createElement('div');
        alertElement.innerHTML = alertHtml;
        container.appendChild(alertElement.firstElementChild);
        
        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, duration);
        }
    },
    
    /**
     * 获取消息类型对应的图标
     */
    getIcon: function(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'exclamation-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    },
    
    /**
     * 确认对话框
     */
    confirm: function(message, callback) {
        if (confirm(message)) {
            callback();
        }
    },
    
    /**
     * 格式化日期
     */
    formatDate: function(date, format = 'YYYY-MM-DD HH:mm:ss') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },
    
    /**
     * 防抖函数
     */
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    /**
     * 节流函数
     */
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};

// 表单处理
const FormHandler = {
    /**
     * 序列化表单数据
     */
    serialize: function(form) {
        const formData = new FormData(form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            if (data[key]) {
                if (Array.isArray(data[key])) {
                    data[key].push(value);
                } else {
                    data[key] = [data[key], value];
                }
            } else {
                data[key] = value;
            }
        }
        
        return data;
    },
    
    /**
     * 提交表单
     */
    submit: function(form, options = {}) {
        const formData = this.serialize(form);
        const url = options.url || form.action;
        const method = options.method || form.method || 'POST';
        
        // 转换为URL编码格式
        const body = Object.keys(formData)
            .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(formData[key]))
            .join('&');
        
        return Utils.ajax(url, {
            method: method,
            body: body
        });
    },
    
    /**
     * 重置表单
     */
    reset: function(form) {
        form.reset();
        // 清除验证状态
        form.querySelectorAll('.is-invalid').forEach(el => {
            el.classList.remove('is-invalid');
        });
        form.querySelectorAll('.invalid-feedback').forEach(el => {
            el.remove();
        });
    },
    
    /**
     * 显示表单验证错误
     */
    showErrors: function(form, errors) {
        // 清除之前的错误
        form.querySelectorAll('.is-invalid').forEach(el => {
            el.classList.remove('is-invalid');
        });
        form.querySelectorAll('.invalid-feedback').forEach(el => {
            el.remove();
        });
        
        // 显示新错误
        Object.keys(errors).forEach(field => {
            const input = form.querySelector(`[name="${field}"]`);
            if (input) {
                input.classList.add('is-invalid');
                
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = errors[field];
                input.parentNode.appendChild(feedback);
            }
        });
    }
};

// 表格处理
const TableHandler = {
    /**
     * 初始化数据表格
     */
    init: function(tableId, options = {}) {
        const table = document.getElementById(tableId);
        if (!table) return;
        
        // 添加排序功能
        if (options.sortable !== false) {
            this.addSorting(table);
        }
        
        // 添加搜索功能
        if (options.searchable !== false) {
            this.addSearch(table);
        }
        
        // 添加分页功能
        if (options.pagination !== false) {
            this.addPagination(table, options.pageSize || 10);
        }
    },
    
    /**
     * 添加排序功能
     */
    addSorting: function(table) {
        const headers = table.querySelectorAll('th[data-sortable]');
        
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.innerHTML += ' <i class="fas fa-sort text-muted"></i>';
            
            header.addEventListener('click', () => {
                const column = header.dataset.sortable;
                const currentOrder = header.dataset.order || 'asc';
                const newOrder = currentOrder === 'asc' ? 'desc' : 'asc';
                
                this.sortTable(table, column, newOrder);
                
                // 更新排序图标
                headers.forEach(h => {
                    h.querySelector('i').className = 'fas fa-sort text-muted';
                    delete h.dataset.order;
                });
                
                header.dataset.order = newOrder;
                header.querySelector('i').className = `fas fa-sort-${newOrder === 'asc' ? 'up' : 'down'}`;
            });
        });
    },
    
    /**
     * 排序表格
     */
    sortTable: function(table, column, order) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        rows.sort((a, b) => {
            const aValue = a.querySelector(`[data-sort="${column}"]`)?.textContent || '';
            const bValue = b.querySelector(`[data-sort="${column}"]`)?.textContent || '';
            
            if (order === 'asc') {
                return aValue.localeCompare(bValue, undefined, { numeric: true });
            } else {
                return bValue.localeCompare(aValue, undefined, { numeric: true });
            }
        });
        
        rows.forEach(row => tbody.appendChild(row));
    },
    
    /**
     * 添加搜索功能
     */
    addSearch: function(table) {
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'form-control mb-3';
        searchInput.placeholder = '搜索...';
        
        table.parentNode.insertBefore(searchInput, table);
        
        searchInput.addEventListener('input', Utils.debounce(() => {
            this.filterTable(table, searchInput.value);
        }, 300));
    },
    
    /**
     * 过滤表格
     */
    filterTable: function(table, searchTerm) {
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const matches = text.includes(searchTerm.toLowerCase());
            row.style.display = matches ? '' : 'none';
        });
    }
};

// 文件上传处理
const FileUploader = {
    /**
     * 初始化文件上传
     */
    init: function(element, options = {}) {
        const dropZone = element;
        
        // 拖拽上传
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });
        
        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragover');
        });
        
        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            this.handleFiles(files, options);
        });
        
        // 点击上传
        const fileInput = dropZone.querySelector('input[type="file"]');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                this.handleFiles(e.target.files, options);
            });
        }
    },
    
    /**
     * 处理文件
     */
    handleFiles: function(files, options) {
        Array.from(files).forEach(file => {
            if (this.validateFile(file, options)) {
                this.uploadFile(file, options);
            }
        });
    },
    
    /**
     * 验证文件
     */
    validateFile: function(file, options) {
        // 检查文件大小
        if (options.maxSize && file.size > options.maxSize) {
            Utils.showMessage(`文件 ${file.name} 超过最大大小限制`, 'error');
            return false;
        }
        
        // 检查文件类型
        if (options.allowedTypes && !options.allowedTypes.includes(file.type)) {
            Utils.showMessage(`文件 ${file.name} 类型不支持`, 'error');
            return false;
        }
        
        return true;
    },
    
    /**
     * 上传文件
     */
    uploadFile: function(file, options) {
        const formData = new FormData();
        formData.append('file', file);
        
        const xhr = new XMLHttpRequest();
        
        // 上传进度
        xhr.upload.addEventListener('progress', (e) => {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 100;
                if (options.onProgress) {
                    options.onProgress(percentComplete, file);
                }
            }
        });
        
        // 上传完成
        xhr.addEventListener('load', () => {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (options.onSuccess) {
                        options.onSuccess(response, file);
                    }
                } catch (e) {
                    if (options.onError) {
                        options.onError('响应格式错误', file);
                    }
                }
            } else {
                if (options.onError) {
                    options.onError(`上传失败: ${xhr.statusText}`, file);
                }
            }
        });
        
        // 上传错误
        xhr.addEventListener('error', () => {
            if (options.onError) {
                options.onError('网络错误', file);
            }
        });
        
        xhr.open('POST', options.uploadUrl || '/upload');
        xhr.send(formData);
    }
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 初始化弹出框
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // 自动关闭警告框
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(alert => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
    
    // 确认删除
    document.addEventListener('click', function(e) {
        if (e.target.matches('[data-confirm]')) {
            e.preventDefault();
            const message = e.target.dataset.confirm || '确定要执行此操作吗？';
            Utils.confirm(message, () => {
                if (e.target.href) {
                    window.location.href = e.target.href;
                } else if (e.target.form) {
                    e.target.form.submit();
                }
            });
        }
    });
});

// 导出到全局
window.AdminUtils = Utils;
window.FormHandler = FormHandler;
window.TableHandler = TableHandler;
window.FileUploader = FileUploader;
