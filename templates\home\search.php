<?php
$content = ob_start();
?>

<div class="container mt-4">
    <!-- 搜索标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-search me-2"></i>
                        搜索结果
                    </h2>
                    <?php if (!empty($keyword)): ?>
                    <p class="text-muted mb-0">
                        关键词：<strong>"<?= $this->escape($keyword) ?>"</strong>
                        <?php if ($total > 0): ?>
                        - 找到 <strong><?= $total ?></strong> 个结果
                        <?php endif; ?>
                    </p>
                    <?php endif; ?>
                </div>
                
                <div>
                    <a href="<?= $this->url() ?>" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-1"></i>
                        返回首页
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索框 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form action="<?= $this->url('home', 'search') ?>" method="GET" class="row g-3">
                        <div class="col-md-10">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control form-control-lg" 
                                       name="q" placeholder="搜索网站标题、描述或URL..." 
                                       value="<?= $this->escape($keyword) ?>" 
                                       autocomplete="off" autofocus>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-search me-1"></i>
                                搜索
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索结果 -->
    <?php if (!empty($keyword)): ?>
        <?php if (!empty($results)): ?>
        <div class="row">
            <?php foreach ($results as $link): ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card link-card h-100" data-link-id="<?= $link['id'] ?>">
                    <div class="card-body d-flex flex-column">
                        <div class="d-flex align-items-center mb-3">
                            <?php if (!empty($link['icon'])): ?>
                            <img src="<?= $this->escape($link['icon']) ?>" 
                                 alt="<?= $this->escape($link['title']) ?>" 
                                 class="link-icon me-2"
                                 onerror="this.src='<?= $this->asset('images/default-icon.png') ?>'">
                            <?php else: ?>
                            <i class="fas fa-link link-icon me-2"></i>
                            <?php endif; ?>
                            <h6 class="card-title mb-0 flex-grow-1">
                                <?= $this->escape($link['title']) ?>
                            </h6>
                        </div>
                        
                        <?php if (!empty($link['description'])): ?>
                        <p class="card-text text-muted small flex-grow-1">
                            <?= $this->escape($this->truncate($link['description'], 100)) ?>
                        </p>
                        <?php endif; ?>

                        <div class="mb-2">
                            <small class="text-muted d-block">
                                <i class="fas fa-link me-1"></i>
                                <?= $this->escape($this->truncate($link['url'], 50)) ?>
                            </small>
                        </div>
                        
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-mouse-pointer me-1"></i>
                                    <?= $link['click_count'] ?? 0 ?> 次点击
                                </small>
                                <a href="<?= $this->url('link', 'go', ['id' => $link['id']]) ?>" 
                                   class="btn btn-sm btn-primary"
                                   target="_blank"
                                   onclick="recordClick(<?= $link['id'] ?>)">
                                    访问 <i class="fas fa-external-link-alt"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- 搜索建议 -->
        <?php if ($total < 5): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>搜索建议：</h6>
                    <ul class="mb-0">
                        <li>尝试使用更简短的关键词</li>
                        <li>检查关键词的拼写是否正确</li>
                        <li>尝试使用同义词或相关词汇</li>
                        <li>使用空格分隔多个关键词</li>
                    </ul>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <?php else: ?>
        <!-- 无搜索结果 -->
        <div class="row">
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">未找到相关结果</h4>
                    <p class="text-muted">
                        没有找到包含 "<strong><?= $this->escape($keyword) ?></strong>" 的网站
                    </p>
                    
                    <div class="mt-4">
                        <a href="<?= $this->url() ?>" class="btn btn-primary me-2">
                            <i class="fas fa-home me-1"></i>
                            浏览所有网站
                        </a>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearSearch()">
                            <i class="fas fa-times me-1"></i>
                            清空搜索
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    <?php else: ?>
    <!-- 空搜索状态 -->
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">开始搜索</h4>
                <p class="text-muted">输入关键词搜索您需要的网站</p>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
// 清空搜索
function clearSearch() {
    document.querySelector('input[name="q"]').value = '';
    document.querySelector('input[name="q"]').focus();
}

// 高亮关键词
document.addEventListener('DOMContentLoaded', function() {
    const keyword = '<?= addslashes($keyword) ?>';
    if (keyword) {
        highlightKeywords(keyword);
    }
});

function highlightKeywords(keyword) {
    const elements = document.querySelectorAll('.card-title, .card-text, small');
    const regex = new RegExp(`(${keyword})`, 'gi');

    elements.forEach(element => {
        if (element.innerHTML && !element.querySelector('.search-highlight')) {
            element.innerHTML = element.innerHTML.replace(regex, '<span class="search-highlight">$1</span>');
        }
    });
}
</script>

<?php
$content = ob_get_clean();
include TEMPLATE_PATH . '/layout.php';
?>
