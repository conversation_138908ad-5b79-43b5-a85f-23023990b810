<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $this->escape($title ?? SITE_NAME) ?></title>
    <meta name="description" content="<?= $this->escape($description ?? SITE_DESCRIPTION) ?>">
    <meta name="keywords" content="导航,网址,书签,工具">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- WebStack主题样式 -->
    <link href="<?= $this->asset('css/webstack.css') ?>" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --info-color: #17a2b8;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
        }
        
        .main-container {
            padding-top: 100px;
            padding-bottom: 50px;
        }
        
        .search-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }
        
        .search-tabs {
            margin-bottom: 20px;
        }
        
        .search-tabs .nav-link {
            border: none;
            background: transparent;
            color: var(--secondary-color);
            font-weight: 500;
            padding: 10px 20px;
            border-radius: 25px;
            margin-right: 10px;
            transition: all 0.3s ease;
        }
        
        .search-tabs .nav-link.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
        }
        
        .search-input-group {
            position: relative;
        }
        
        .search-input {
            border: 2px solid #e9ecef;
            border-radius: 50px;
            padding: 15px 60px 15px 25px;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: none;
        }
        
        .search-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .search-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            border: none;
            background: var(--primary-color);
            color: white;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        
        .search-btn:hover {
            background: #0056b3;
            transform: translateY(-50%) scale(1.05);
        }
        
        .category-section {
            margin-bottom: 40px;
        }
        
        .category-title {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px 30px;
            margin-bottom: 20px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            border-left: 5px solid var(--primary-color);
        }
        
        .category-title h3 {
            margin: 0;
            color: var(--dark-color);
            font-weight: 600;
            display: flex;
            align-items: center;
        }
        
        .category-title h3 i {
            margin-right: 15px;
            color: var(--primary-color);
        }
        
        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
        }
        
        .link-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }
        
        .link-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            color: inherit;
        }
        
        .link-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--light-color);
            overflow: hidden;
        }
        
        .link-icon img {
            width: 32px;
            height: 32px;
            object-fit: cover;
        }
        
        .link-icon i {
            font-size: 24px;
            color: var(--primary-color);
        }
        
        .link-title {
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 8px;
            color: var(--dark-color);
        }
        
        .link-description {
            font-size: 14px;
            color: var(--secondary-color);
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .link-meta {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: var(--secondary-color);
        }
        
        .click-count {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
        }
        
        .footer {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-top: 50px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }
        
        .news-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }
        
        .news-title {
            color: var(--dark-color);
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .news-title i {
            margin-right: 10px;
            color: var(--danger-color);
        }
        
        .news-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .news-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        
        .news-item:last-child {
            border-bottom: none;
        }
        
        .news-index {
            background: var(--primary-color);
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin-right: 10px;
            flex-shrink: 0;
        }
        
        .news-link {
            color: var(--dark-color);
            text-decoration: none;
            font-size: 14px;
            line-height: 1.4;
            flex: 1;
        }
        
        .news-link:hover {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        @media (max-width: 768px) {
            .main-container {
                padding-top: 80px;
            }
            
            .search-section {
                padding: 25px;
                margin-bottom: 25px;
            }
            
            .links-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .category-title {
                padding: 15px 20px;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="<?= $this->url() ?>">
                <i class="fas fa-compass"></i>
                <?= SITE_NAME ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <?php if (!empty($categories)): ?>
                        <?php foreach ($categories as $category): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="#category-<?= $category['id'] ?>">
                                <i class="<?= $category['icon'] ?: 'fas fa-folder' ?>"></i>
                                <?= $this->escape($category['name']) ?>
                            </a>
                        </li>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="<?= $this->url('guestbook') ?>">
                            <i class="fas fa-comments"></i>
                            留言板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= $this->url('submission') ?>">
                            <i class="fas fa-paper-plane"></i>
                            网站提交
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= $this->url('admin') ?>">
                            <i class="fas fa-cog"></i>
                            管理
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container main-container">
        <!-- 搜索区域 -->
        <div class="search-section">
            <div class="text-center mb-4">
                <h1 class="display-6 fw-bold text-primary mb-3"><?= SITE_NAME ?></h1>
                <p class="lead text-muted"><?= SITE_DESCRIPTION ?></p>
            </div>
            
            <!-- 搜索标签 -->
            <ul class="nav nav-pills justify-content-center search-tabs" id="searchTabs">
                <li class="nav-item">
                    <a class="nav-link active" data-engine="site" href="#">站内</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-engine="baidu" href="#">百度</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-engine="bing" href="#">必应</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-engine="google" href="#">谷歌</a>
                </li>
            </ul>
            
            <!-- 搜索框 -->
            <div class="search-input-group">
                <input type="text" class="form-control search-input" id="searchInput" 
                       placeholder="搜索网站、工具或资源...">
                <button class="search-btn" onclick="performSearch()">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>

        <!-- 热点新闻 -->
        <?php if (!empty($hotNews)): ?>
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="news-section">
                    <h5 class="news-title">
                        <i class="fas fa-fire"></i>
                        热点新闻
                    </h5>
                    <ul class="news-list">
                        <?php foreach (array_slice($hotNews, 0, 10) as $index => $news): ?>
                        <li class="news-item">
                            <span class="news-index"><?= $index + 1 ?></span>
                            <a href="<?= $this->escape($news['url']) ?>" target="_blank" class="news-link">
                                <?= $this->escape($this->truncate($news['title'], 50)) ?>
                            </a>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
            <div class="col-md-6">
                <div class="news-section">
                    <h5 class="news-title">
                        <i class="fas fa-chart-line"></i>
                        百度热搜
                    </h5>
                    <ul class="news-list">
                        <?php foreach (array_slice($baiduHot ?? [], 0, 10) as $index => $hot): ?>
                        <li class="news-item">
                            <span class="news-index"><?= $index + 1 ?></span>
                            <a href="<?= $this->escape($hot['url']) ?>" target="_blank" class="news-link">
                                <?= $this->escape($this->truncate($hot['title'], 50)) ?>
                            </a>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- 链接分类 -->
        <?php if (!empty($categoriesWithLinks)): ?>
            <?php foreach ($categoriesWithLinks as $category): ?>
            <div class="category-section" id="category-<?= $category['id'] ?>">
                <div class="category-title">
                    <h3>
                        <i class="<?= $category['icon'] ?: 'fas fa-folder' ?>"></i>
                        <?= $this->escape($category['name']) ?>
                        <small class="text-muted ms-2">(<?= count($category['links']) ?>)</small>
                    </h3>
                </div>
                
                <?php if (!empty($category['links'])): ?>
                <div class="links-grid">
                    <?php foreach ($category['links'] as $link): ?>
                    <a href="<?= $this->url('click', 'redirect', ['id' => $link['id']]) ?>" 
                       target="_blank" class="link-card">
                        <div class="link-icon">
                            <?php if (!empty($link['icon'])): ?>
                                <img src="<?= $this->escape($link['icon']) ?>" 
                                     alt="<?= $this->escape($link['title']) ?>"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <i class="fas fa-link" style="display: none;"></i>
                            <?php else: ?>
                                <i class="fas fa-link"></i>
                            <?php endif; ?>
                        </div>
                        
                        <div class="link-title"><?= $this->escape($link['title']) ?></div>
                        
                        <?php if (!empty($link['description'])): ?>
                        <div class="link-description">
                            <?= $this->escape($link['description']) ?>
                        </div>
                        <?php endif; ?>
                        
                        <div class="link-meta">
                            <span><?= $this->formatDate($link['created_at'], 'Y-m-d') ?></span>
                            <span class="click-count">
                                <i class="fas fa-mouse-pointer"></i>
                                <?= $link['click_count'] ?>
                            </span>
                        </div>
                    </a>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted">该分类下暂无链接</p>
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; ?>
        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-4x text-muted mb-4"></i>
            <h4 class="text-muted">暂无内容</h4>
            <p class="text-muted">请联系管理员添加链接</p>
        </div>
        <?php endif; ?>

        <!-- 页脚 -->
        <div class="footer">
            <p class="mb-2">
                <strong><?= SITE_NAME ?></strong> - 精选优质网站导航
            </p>
            <p class="text-muted mb-0">
                Powered by <a href="https://github.com/navstack/navstack" target="_blank" class="text-decoration-none">NavStack</a>
                | Copyright © <?= date('Y') ?> All rights reserved.
            </p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 搜索引擎配置
        const searchEngines = {
            site: {
                name: '站内搜索',
                url: '<?= $this->url("search") ?>?q=',
                placeholder: '搜索站内链接...'
            },
            baidu: {
                name: '百度搜索',
                url: 'https://www.baidu.com/s?wd=',
                placeholder: '百度一下，你就知道'
            },
            bing: {
                name: '必应搜索',
                url: 'https://www.bing.com/search?q=',
                placeholder: 'Microsoft Bing 搜索'
            },
            google: {
                name: '谷歌搜索',
                url: 'https://www.google.com/search?q=',
                placeholder: 'Google 搜索'
            }
        };
        
        let currentEngine = 'site';
        
        // 切换搜索引擎
        document.querySelectorAll('#searchTabs .nav-link').forEach(tab => {
            tab.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 更新激活状态
                document.querySelectorAll('#searchTabs .nav-link').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                // 更新当前引擎
                currentEngine = this.dataset.engine;
                
                // 更新搜索框占位符
                const searchInput = document.getElementById('searchInput');
                searchInput.placeholder = searchEngines[currentEngine].placeholder;
            });
        });
        
        // 执行搜索
        function performSearch() {
            const query = document.getElementById('searchInput').value.trim();
            if (!query) return;
            
            const engine = searchEngines[currentEngine];
            if (currentEngine === 'site') {
                window.location.href = engine.url + encodeURIComponent(query);
            } else {
                window.open(engine.url + encodeURIComponent(query), '_blank');
            }
        }
        
        // 回车搜索
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
        
        // 平滑滚动到分类
        document.querySelectorAll('a[href^="#category-"]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // 懒加载图片
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    </script>
</body>
</html>
