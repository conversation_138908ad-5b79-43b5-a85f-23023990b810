<?php
/**
 * 测试安装脚本
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('TEMPLATE_PATH', ROOT_PATH . '/templates');
define('PUBLIC_PATH', ROOT_PATH . '/public');

// 数据库配置
define('DB_TYPE', 'sqlite');
define('DB_PATH', ROOT_PATH . '/data/navstack.db');

// 站点配置
define('SITE_NAME', 'TwoNav高级版');
define('SITE_DESCRIPTION', '精选优质网站导航');
define('DEFAULT_THEME', 'webstack');

// 管理员配置
define('ADMIN_USERNAME', 'admin');
define('ADMIN_PASSWORD', 'admin123');
define('ADMIN_EMAIL', '<EMAIL>');

// 自动加载
spl_autoload_register(function ($class) {
    $file = APP_PATH . '/' . str_replace('\\', '/', $class) . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

try {
    echo "开始安装TwoNav高级版...\n\n";
    
    // 检查目录
    echo "检查目录结构...\n";
    $dirs = [
        ROOT_PATH . '/data',
        ROOT_PATH . '/public/static/css',
        ROOT_PATH . '/public/static/js',
        ROOT_PATH . '/public/static/images',
        ROOT_PATH . '/templates/admin',
        ROOT_PATH . '/templates/themes/webstack'
    ];
    
    foreach ($dirs as $dir) {
        if (!is_dir($dir)) {
            if (@mkdir($dir, 0755, true)) {
                echo "创建目录: $dir\n";
            } else {
                echo "警告: 无法创建目录 $dir (权限不足)\n";
            }
        } else {
            echo "目录已存在: $dir\n";
        }
    }
    
    // 初始化数据库
    echo "\n初始化数据库...\n";
    $installer = new Core\Installer();
    $installer->install();
    
    echo "\n安装完成！\n";
    echo "管理员账号: " . ADMIN_USERNAME . "\n";
    echo "管理员密码: " . ADMIN_PASSWORD . "\n";
    echo "访问地址: http://localhost:8000\n";
    echo "管理后台: http://localhost:8000/index.php?c=admin\n";
    
} catch (Exception $e) {
    echo "安装失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
?>
