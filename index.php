<?php
/**
 * NavStack - 网址导航管理系统
 * 入口文件
 */

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('TEMPLATE_PATH', ROOT_PATH . '/templates');
define('STATIC_PATH', ROOT_PATH . '/static');
define('DATA_PATH', ROOT_PATH . '/data');

// 自动加载
spl_autoload_register(function ($class) {
    $file = APP_PATH . '/' . str_replace('\\', '/', $class) . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// 引入配置文件
require_once ROOT_PATH . '/config.php';

// 启动应用
try {
    $app = new Core\Application();
    $app->run();
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
