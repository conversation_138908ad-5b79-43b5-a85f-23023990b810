<?php
/**
 * TwoNav高级版重新安装程序
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('TEMPLATE_PATH', ROOT_PATH . '/templates');
define('PUBLIC_PATH', ROOT_PATH . '/public');

// 数据库配置 - 自动检测
if (file_exists(ROOT_PATH . '/config/database.php')) {
    require_once ROOT_PATH . '/config/database.php';
} else {
    // 默认使用SQLite
    define('DB_TYPE', 'sqlite');
    define('DB_PATH', ROOT_PATH . '/data/navstack.db');
}

// 站点配置
define('SITE_NAME', 'TwoNav高级版');
define('SITE_DESCRIPTION', '精选优质网站导航');
define('DEFAULT_THEME', 'webstack');

// 管理员配置
define('ADMIN_USERNAME', 'admin');
define('ADMIN_PASSWORD', 'admin123');
define('ADMIN_EMAIL', '<EMAIL>');

// 自动加载
spl_autoload_register(function ($class) {
    $file = APP_PATH . '/' . str_replace('\\', '/', $class) . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

echo "<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>TwoNav高级版 - 重新安装</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { background: rgba(255,255,255,0.95); padding: 30px; border-radius: 20px; box-shadow: 0 10px 40px rgba(0,0,0,0.1); }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .step { margin: 15px 0; padding: 10px; border-left: 4px solid #007bff; background: #f8f9fa; }
        h1 { color: #007bff; text-align: center; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; }
        .btn:hover { background: #0056b3; }
        .btn.danger { background: #dc3545; }
        .btn.danger:hover { background: #c82333; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🔄 TwoNav高级版重新安装</h1>";

try {
    echo "<div class='step'>开始重新安装TwoNav高级版...</div>";
    
    // 备份现有数据库
    if (file_exists(DB_PATH)) {
        $backupPath = DB_PATH . '.backup.' . date('YmdHis');
        if (copy(DB_PATH, $backupPath)) {
            echo "<div class='success'>✅ 已备份现有数据库到: " . basename($backupPath) . "</div>";
        }
        
        // 删除现有数据库
        if (unlink(DB_PATH)) {
            echo "<div class='success'>✅ 已删除现有数据库</div>";
        }
    }
    
    // 创建必要目录
    echo "<div class='step'>📁 检查目录结构...</div>";
    $dirs = [
        ROOT_PATH . '/data',
        ROOT_PATH . '/public/static/css',
        ROOT_PATH . '/public/static/js',
        ROOT_PATH . '/public/static/images',
        ROOT_PATH . '/templates/admin',
        ROOT_PATH . '/templates/themes/webstack'
    ];
    
    foreach ($dirs as $dir) {
        if (!is_dir($dir)) {
            if (@mkdir($dir, 0755, true)) {
                echo "<div class='success'>✅ 创建目录: $dir</div>";
            } else {
                echo "<div class='warning'>⚠️ 警告: 无法创建目录 $dir (权限不足，但可能不影响使用)</div>";
            }
        } else {
            echo "<div class='success'>✅ 目录已存在: $dir</div>";
        }
    }
    
    // 重新安装数据库
    echo "<div class='step'>🗄️ 重新安装数据库...</div>";
    
    $installer = new Core\Installer();
    $installer->install();
    
    echo "<div class='success'>✅ 数据库重新安装成功！</div>";
    
    echo "<div class='step'>🎉 重新安装完成！</div>";
    echo "<div class='success'>
        <h3>安装信息</h3>
        <p><strong>管理员账号：</strong>" . ADMIN_USERNAME . "</p>
        <p><strong>管理员密码：</strong>" . ADMIN_PASSWORD . "</p>
        <p><strong>安装时间：</strong>" . date('Y-m-d H:i:s') . "</p>
        <p><strong>数据库文件：</strong>" . DB_PATH . "</p>
    </div>";
    
    echo "<div style='text-align: center; margin-top: 30px;'>
        <a href='index.php' class='btn'>🏠 访问首页</a>
        <a href='index.php?c=admin' class='btn'>⚙️ 管理后台</a>
        <a href='demo.html' class='btn'>📖 查看演示</a>
    </div>";
    
    echo "<div style='text-align: center; margin-top: 20px;'>
        <small style='color: #666;'>
            🔒 为了安全，请删除 reinstall.php 文件
        </small>
    </div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ 重新安装失败: " . htmlspecialchars($e->getMessage()) . "</div>";
    
    echo "<div style='margin-top: 20px;'>
        <h4>可能的解决方案：</h4>
        <ul>
            <li>手动删除数据库文件: <code>" . DB_PATH . "</code></li>
            <li>检查 data 目录是否有写入权限</li>
            <li>确保 PHP 支持 SQLite</li>
            <li>检查服务器错误日志</li>
        </ul>
        
        <div style='text-align: center; margin-top: 20px;'>
            <a href='javascript:history.back()' class='btn'>🔙 返回</a>
            <a href='reinstall.php' class='btn danger'>🔄 重试</a>
        </div>
    </div>";
}

echo "
    </div>
</body>
</html>";
?>
