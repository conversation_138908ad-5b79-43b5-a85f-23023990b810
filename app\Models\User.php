<?php

namespace Models;

use Core\Model;

/**
 * 用户模型
 */
class User extends Model
{
    protected $table = 'users';
    
    /**
     * 根据用户名查找用户
     */
    public function findByUsername($username)
    {
        return $this->findOne('username = :username', ['username' => $username]);
    }
    
    /**
     * 根据邮箱查找用户
     */
    public function findByEmail($email)
    {
        return $this->findOne('email = :email', ['email' => $email]);
    }
    
    /**
     * 验证用户密码
     */
    public function verifyPassword($password, $hash)
    {
        return password_verify($password, $hash);
    }
    
    /**
     * 创建用户
     */
    public function createUser($data)
    {
        // 加密密码
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        return $this->create($data);
    }
    
    /**
     * 更新用户密码
     */
    public function updatePassword($userId, $newPassword)
    {
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        return $this->update($userId, ['password' => $hashedPassword]);
    }
    
    /**
     * 更新最后登录信息
     */
    public function updateLastLogin($userId, $ip = '')
    {
        return $this->update($userId, [
            'last_login_time' => date('Y-m-d H:i:s'),
            'last_login_ip' => $ip
        ]);
    }
    
    /**
     * 获取用户统计信息
     */
    public function getUserStats($userId)
    {
        $sql = "SELECT 
                    COUNT(DISTINCT l.id) as link_count,
                    COUNT(DISTINCT c.id) as category_count,
                    COUNT(DISTINCT a.id) as article_count
                FROM users u
                LEFT JOIN links l ON u.id = l.user_id AND l.status = 1
                LEFT JOIN categories c ON u.id = c.user_id AND c.status = 1
                LEFT JOIN articles a ON u.id = a.author_id AND a.status = 1
                WHERE u.id = :user_id";
        
        return $this->db->fetch($sql, ['user_id' => $userId]);
    }
    
    /**
     * 获取用户权限
     */
    public function getUserPermissions($userId)
    {
        $sql = "SELECT ug.permissions 
                FROM users u 
                LEFT JOIN user_groups ug ON u.group_id = ug.id 
                WHERE u.id = :user_id";
        
        $result = $this->db->fetch($sql, ['user_id' => $userId]);
        return $result ? $result['permissions'] : '';
    }
    
    /**
     * 检查用户权限
     */
    public function hasPermission($userId, $permission)
    {
        $permissions = $this->getUserPermissions($userId);
        
        if ($permissions === 'all') {
            return true;
        }
        
        $permissionArray = explode(',', $permissions);
        return in_array($permission, $permissionArray);
    }
    
    /**
     * 获取活跃用户
     */
    public function getActiveUsers($limit = 10)
    {
        return $this->findAll('status = 1', [], 'last_login_time DESC', $limit);
    }
    
    /**
     * 用户名是否存在
     */
    public function usernameExists($username, $excludeId = null)
    {
        $where = 'username = :username';
        $params = ['username' => $username];
        
        if ($excludeId) {
            $where .= ' AND id != :id';
            $params['id'] = $excludeId;
        }
        
        return $this->findOne($where, $params) !== false;
    }
    
    /**
     * 邮箱是否存在
     */
    public function emailExists($email, $excludeId = null)
    {
        $where = 'email = :email';
        $params = ['email' => $email];
        
        if ($excludeId) {
            $where .= ' AND id != :id';
            $params['id'] = $excludeId;
        }
        
        return $this->findOne($where, $params) !== false;
    }
    
    /**
     * 禁用用户
     */
    public function disableUser($userId)
    {
        return $this->update($userId, ['status' => 0]);
    }
    
    /**
     * 启用用户
     */
    public function enableUser($userId)
    {
        return $this->update($userId, ['status' => 1]);
    }
}
