<?php

namespace Models;

use Core\Model;

/**
 * 文章模型
 */
class Article extends Model
{
    protected $table = 'articles';
    
    /**
     * 获取已发布的文章
     */
    public function getPublishedArticles($limit = null)
    {
        $limitStr = $limit ? $limit : '';
        return $this->findAll('status = 1', [], 'created_at DESC', $limitStr);
    }
    
    /**
     * 获取文章详情（包含作者信息）
     */
    public function getArticleWithAuthor($id)
    {
        $sql = "SELECT a.*, u.username as author_name, u.nickname as author_nickname
                FROM {$this->table} a
                LEFT JOIN users u ON a.author_id = u.id
                WHERE a.id = :id";
        
        return $this->db->fetch($sql, ['id' => $id]);
    }
    
    /**
     * 获取文章列表（包含作者信息）
     */
    public function getArticlesWithAuthor($where = '1=1', $params = [], $limit = null)
    {
        $sql = "SELECT a.*, u.username as author_name, u.nickname as author_nickname
                FROM {$this->table} a
                LEFT JOIN users u ON a.author_id = u.id
                WHERE {$where}
                ORDER BY a.created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * 搜索文章
     */
    public function searchArticles($keyword, $limit = 20)
    {
        $where = 'status = 1 AND (title LIKE :keyword OR content LIKE :keyword OR tags LIKE :keyword)';
        $params = ['keyword' => "%{$keyword}%"];
        
        return $this->getArticlesWithAuthor($where, $params, $limit);
    }
    
    /**
     * 获取热门文章
     */
    public function getPopularArticles($limit = 10)
    {
        return $this->getArticlesWithAuthor('status = 1', [], $limit);
    }
    
    /**
     * 获取最新文章
     */
    public function getLatestArticles($limit = 10)
    {
        return $this->getArticlesWithAuthor('status = 1', [], $limit);
    }
    
    /**
     * 增加文章浏览量
     */
    public function incrementViewCount($id)
    {
        $sql = "UPDATE {$this->table} SET view_count = view_count + 1 WHERE id = :id";
        return $this->db->query($sql, ['id' => $id]);
    }
    
    /**
     * 获取文章统计信息
     */
    public function getArticleStats()
    {
        $sql = "SELECT 
                    COUNT(*) as total_articles,
                    COUNT(CASE WHEN status = 1 THEN 1 END) as published_articles,
                    SUM(view_count) as total_views,
                    AVG(view_count) as avg_views
                FROM {$this->table}";
        
        return $this->db->fetch($sql);
    }
    
    /**
     * 根据标签获取文章
     */
    public function getArticlesByTag($tag, $limit = 20)
    {
        $where = 'status = 1 AND tags LIKE :tag';
        $params = ['tag' => "%{$tag}%"];
        
        return $this->getArticlesWithAuthor($where, $params, $limit);
    }
    
    /**
     * 获取所有标签
     */
    public function getAllTags()
    {
        $sql = "SELECT tags FROM {$this->table} WHERE status = 1 AND tags != ''";
        $articles = $this->db->fetchAll($sql);
        
        $allTags = [];
        foreach ($articles as $article) {
            $tags = explode(',', $article['tags']);
            foreach ($tags as $tag) {
                $tag = trim($tag);
                if (!empty($tag) && !in_array($tag, $allTags)) {
                    $allTags[] = $tag;
                }
            }
        }
        
        return $allTags;
    }
    
    /**
     * 获取相关文章
     */
    public function getRelatedArticles($articleId, $tags, $limit = 5)
    {
        if (empty($tags)) {
            return [];
        }
        
        $tagArray = explode(',', $tags);
        $conditions = [];
        $params = ['article_id' => $articleId];
        
        foreach ($tagArray as $index => $tag) {
            $tag = trim($tag);
            if (!empty($tag)) {
                $conditions[] = "tags LIKE :tag{$index}";
                $params["tag{$index}"] = "%{$tag}%";
            }
        }
        
        if (empty($conditions)) {
            return [];
        }
        
        $where = 'status = 1 AND id != :article_id AND (' . implode(' OR ', $conditions) . ')';
        
        return $this->getArticlesWithAuthor($where, $params, $limit);
    }
    
    /**
     * 发布文章
     */
    public function publishArticle($id)
    {
        return $this->update($id, ['status' => 1]);
    }
    
    /**
     * 取消发布文章
     */
    public function unpublishArticle($id)
    {
        return $this->update($id, ['status' => 0]);
    }
    
    /**
     * 软删除文章
     */
    public function softDeleteArticle($id)
    {
        return $this->update($id, ['status' => -1]);
    }
    
    /**
     * 恢复文章
     */
    public function restoreArticle($id)
    {
        return $this->update($id, ['status' => 1]);
    }
    
    /**
     * 生成文章摘要
     */
    public function generateExcerpt($content, $length = 200)
    {
        // 移除HTML标签
        $text = strip_tags($content);
        
        // 截取指定长度
        if (mb_strlen($text, 'UTF-8') <= $length) {
            return $text;
        }
        
        return mb_substr($text, 0, $length, 'UTF-8') . '...';
    }
}
