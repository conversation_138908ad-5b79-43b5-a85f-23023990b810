<?php
/**
 * TwoNav高级版安装程序
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('TEMPLATE_PATH', ROOT_PATH . '/templates');
define('PUBLIC_PATH', ROOT_PATH . '/public');

// 数据库配置
define('DB_TYPE', 'sqlite');
define('DB_PATH', ROOT_PATH . '/data/navstack.db');

// 站点配置
define('SITE_NAME', 'TwoNav高级版');
define('SITE_DESCRIPTION', '精选优质网站导航');
define('DEFAULT_THEME', 'webstack');

// 管理员配置
define('ADMIN_USERNAME', 'admin');
define('ADMIN_PASSWORD', 'admin123');
define('ADMIN_EMAIL', '<EMAIL>');

// 自动加载
spl_autoload_register(function ($class) {
    $file = APP_PATH . '/' . str_replace('\\', '/', $class) . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// 处理安装请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'install') {
    header('Content-Type: application/json');
    
    try {
        // 创建必要目录
        $dirs = [
            ROOT_PATH . '/data',
            ROOT_PATH . '/public/static/css',
            ROOT_PATH . '/public/static/js',
            ROOT_PATH . '/public/static/images',
            ROOT_PATH . '/templates/admin',
            ROOT_PATH . '/templates/themes/webstack'
        ];
        
        foreach ($dirs as $dir) {
            if (!is_dir($dir)) {
                @mkdir($dir, 0755, true);
            }
        }

        // 如果数据库已存在，先备份再删除
        if (file_exists(DB_PATH)) {
            $backupPath = DB_PATH . '.backup.' . date('YmdHis');
            @copy(DB_PATH, $backupPath);
            @unlink(DB_PATH);
        }

        // 初始化数据库
        $installer = new Core\Installer();
        $installer->install();
        
        echo json_encode(['success' => true, 'message' => '安装成功']);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// 检查是否已安装
if (file_exists(ROOT_PATH . '/data/navstack.db') && !isset($_GET['force'])) {
    echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TwoNav高级版 - 已安装</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; margin-top: 100px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 600px; margin: 0 auto; background: rgba(255,255,255,0.95); padding: 40px; border-radius: 20px; box-shadow: 0 10px 40px rgba(0,0,0,0.1); }
        h1 { color: #007bff; margin-bottom: 20px; }
        .btn { display: inline-block; padding: 12px 24px; margin: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 25px; transition: all 0.3s ease; }
        .btn:hover { background: #0056b3; transform: translateY(-2px); }
        .btn.success { background: #28a745; }
        .btn.success:hover { background: #1e7e34; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 TwoNav高级版已安装</h1>
        <p>系统已经安装完成！</p>
        <div>
            <a href="index.php" class="btn">🏠 访问首页</a>
            <a href="index.php?c=admin" class="btn success">⚙️ 管理后台</a>
        </div>
        <p style="margin-top: 30px;">
            <small>如需重新安装，请访问 <a href="install.php?force=1" style="color: #007bff;">install.php?force=1</a></small>
        </p>
    </div>
</body>
</html>';
    exit;
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TwoNav高级版 - 安装程序</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .install-container {
            max-width: 800px;
            margin: 50px auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }
        .step {
            display: none;
        }
        .step.active {
            display: block;
        }
        .progress-bar {
            height: 8px;
            border-radius: 4px;
        }
        .check-item {
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .check-item:last-child {
            border-bottom: none;
        }
        .status-ok {
            color: #28a745;
        }
        .status-error {
            color: #dc3545;
        }
        .status-warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-container">
            <!-- 标题 -->
            <div class="text-center mb-4">
                <h1 class="display-6 fw-bold text-primary mb-3">
                    <i class="fas fa-compass"></i>
                    TwoNav高级版
                </h1>
                <p class="lead text-muted">开源导航系统安装程序</p>
            </div>

            <!-- 进度条 -->
            <div class="mb-4">
                <div class="progress">
                    <div class="progress-bar" id="progressBar" style="width: 25%"></div>
                </div>
                <div class="d-flex justify-content-between mt-2">
                    <small class="text-muted">环境检查</small>
                    <small class="text-muted">系统配置</small>
                    <small class="text-muted">数据库安装</small>
                    <small class="text-muted">完成</small>
                </div>
            </div>

            <!-- 步骤1: 环境检查 -->
            <div class="step active" id="step1">
                <h3 class="mb-4">
                    <i class="fas fa-check-circle"></i>
                    环境检查
                </h3>
                
                <div id="checkResults">
                    <div class="check-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>PHP版本 (需要 7.4+)</span>
                            <span class="<?= version_compare(PHP_VERSION, '7.4.0', '>=') ? 'status-ok' : 'status-error' ?>">
                                <i class="fas fa-<?= version_compare(PHP_VERSION, '7.4.0', '>=') ? 'check' : 'times' ?>"></i>
                                <?= PHP_VERSION ?>
                            </span>
                        </div>
                    </div>
                    
                    <div class="check-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>PDO扩展</span>
                            <span class="<?= extension_loaded('pdo') ? 'status-ok' : 'status-error' ?>">
                                <i class="fas fa-<?= extension_loaded('pdo') ? 'check' : 'times' ?>"></i>
                                <?= extension_loaded('pdo') ? '已安装' : '未安装' ?>
                            </span>
                        </div>
                    </div>
                    
                    <div class="check-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>SQLite扩展</span>
                            <span class="<?= extension_loaded('sqlite3') ? 'status-ok' : 'status-error' ?>">
                                <i class="fas fa-<?= extension_loaded('sqlite3') ? 'check' : 'times' ?>"></i>
                                <?= extension_loaded('sqlite3') ? '已安装' : '未安装' ?>
                            </span>
                        </div>
                    </div>
                    
                    <div class="check-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>data目录写入权限</span>
                            <span class="<?= is_writable(ROOT_PATH . '/data') || is_writable(ROOT_PATH) ? 'status-ok' : 'status-warning' ?>">
                                <i class="fas fa-<?= is_writable(ROOT_PATH . '/data') || is_writable(ROOT_PATH) ? 'check' : 'exclamation-triangle' ?>"></i>
                                <?= is_writable(ROOT_PATH . '/data') || is_writable(ROOT_PATH) ? '可写' : '权限不足' ?>
                            </span>
                        </div>
                    </div>
                </div>
                
                <div class="text-end mt-4">
                    <button class="btn btn-primary" onclick="nextStep()">
                        下一步 <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>

            <!-- 步骤2: 系统配置 -->
            <div class="step" id="step2">
                <h3 class="mb-4">
                    <i class="fas fa-cog"></i>
                    系统配置
                </h3>
                
                <form id="configForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">站点名称</label>
                                <input type="text" class="form-control" name="site_name" value="TwoNav高级版" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">站点描述</label>
                                <input type="text" class="form-control" name="site_description" value="精选优质网站导航" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">管理员用户名</label>
                                <input type="text" class="form-control" name="admin_username" value="admin" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">管理员密码</label>
                                <input type="password" class="form-control" name="admin_password" value="admin123" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">管理员邮箱</label>
                        <input type="email" class="form-control" name="admin_email" value="<EMAIL>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">默认主题</label>
                        <select class="form-select" name="default_theme">
                            <option value="default">默认主题</option>
                            <option value="webstack" selected>WebStack主题</option>
                        </select>
                    </div>
                </form>
                
                <div class="d-flex justify-content-between mt-4">
                    <button class="btn btn-outline-secondary" onclick="prevStep()">
                        <i class="fas fa-arrow-left"></i> 上一步
                    </button>
                    <button class="btn btn-primary" onclick="nextStep()">
                        下一步 <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>

            <!-- 步骤3: 数据库安装 -->
            <div class="step" id="step3">
                <h3 class="mb-4">
                    <i class="fas fa-database"></i>
                    数据库安装
                </h3>
                
                <div id="installProgress" style="display: none;">
                    <div class="text-center">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">安装中...</span>
                        </div>
                        <p>正在安装数据库，请稍候...</p>
                    </div>
                </div>
                
                <div id="installResult" style="display: none;">
                    <!-- 安装结果将在这里显示 -->
                </div>
                
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> 安装说明</h5>
                    <ul class="mb-0">
                        <li>系统将创建SQLite数据库文件</li>
                        <li>初始化所有必要的数据表</li>
                        <li>插入默认数据和配置</li>
                        <li>创建管理员账号</li>
                    </ul>
                </div>
                
                <div class="d-flex justify-content-between mt-4">
                    <button class="btn btn-outline-secondary" onclick="prevStep()">
                        <i class="fas fa-arrow-left"></i> 上一步
                    </button>
                    <button class="btn btn-primary" id="installBtn" onclick="startInstall()">
                        开始安装 <i class="fas fa-play"></i>
                    </button>
                </div>
            </div>

            <!-- 步骤4: 完成 -->
            <div class="step" id="step4">
                <div class="text-center">
                    <div class="mb-4">
                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                    </div>
                    <h3 class="text-success mb-4">🎉 安装完成！</h3>
                    
                    <div class="alert alert-success">
                        <h5>安装信息</h5>
                        <p class="mb-2"><strong>管理员账号：</strong><span id="finalUsername">admin</span></p>
                        <p class="mb-2"><strong>管理员密码：</strong><span id="finalPassword">admin123</span></p>
                        <p class="mb-0"><strong>安装时间：</strong><?= date('Y-m-d H:i:s') ?></p>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-block">
                        <a href="index.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-home"></i>
                            访问首页
                        </a>
                        <a href="index.php?c=admin" class="btn btn-success btn-lg">
                            <i class="fas fa-cog"></i>
                            管理后台
                        </a>
                    </div>
                    
                    <div class="mt-4">
                        <small class="text-muted">
                            <i class="fas fa-shield-alt"></i>
                            为了安全，请删除 install.php 文件
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentStep = 1;
        const totalSteps = 4;
        
        function updateProgress() {
            const progress = (currentStep / totalSteps) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
        }
        
        function showStep(step) {
            document.querySelectorAll('.step').forEach(el => {
                el.classList.remove('active');
            });
            
            document.getElementById('step' + step).classList.add('active');
            currentStep = step;
            updateProgress();
        }
        
        function nextStep() {
            if (currentStep < totalSteps) {
                if (currentStep === 2) {
                    const form = document.getElementById('configForm');
                    if (!form.checkValidity()) {
                        form.reportValidity();
                        return;
                    }
                }
                showStep(currentStep + 1);
            }
        }
        
        function prevStep() {
            if (currentStep > 1) {
                showStep(currentStep - 1);
            }
        }
        
        function startInstall() {
            const form = document.getElementById('configForm');
            const formData = new FormData(form);
            formData.append('action', 'install');
            
            document.getElementById('installBtn').disabled = true;
            document.getElementById('installProgress').style.display = 'block';
            document.getElementById('installResult').style.display = 'none';
            
            fetch('install.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('installProgress').style.display = 'none';
                document.getElementById('installResult').style.display = 'block';
                
                if (data.success) {
                    document.getElementById('installResult').innerHTML = `
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            数据库安装成功！
                        </div>
                    `;
                    
                    document.getElementById('finalUsername').textContent = formData.get('admin_username');
                    document.getElementById('finalPassword').textContent = formData.get('admin_password');
                    
                    setTimeout(() => {
                        nextStep();
                    }, 1500);
                } else {
                    document.getElementById('installResult').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle"></i>
                            安装失败: ${data.message}
                        </div>
                    `;
                    document.getElementById('installBtn').disabled = false;
                }
            })
            .catch(error => {
                document.getElementById('installProgress').style.display = 'none';
                document.getElementById('installResult').style.display = 'block';
                document.getElementById('installResult').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i>
                        安装失败: ${error.message}
                    </div>
                `;
                document.getElementById('installBtn').disabled = false;
            });
        }
    </script>
</body>
</html>
